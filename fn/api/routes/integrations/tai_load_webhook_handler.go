package integrations

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/tms/tai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

// Tai TMS does not support pulling loads from the TMS, so we need to use the webhook to create loads.
// We have a special persistent webhook token (defined in webhook_token.go)
// that is used to authenticate the webhook.
func TaiLoadWebhookHandler(c *fiber.Ctx) error {
	var body tai.ShipmentResp
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext())
	userServiceID := middleware.ServiceIDFromContext(c)

	integration, err := integrationDB.GetByName(ctx, userServiceID, models.Tai)
	if err != nil {
		log.Error(
			ctx,
			"integration not configured properly",
			zap.Error(err), zap.Uint("service_id", userServiceID),
		)

		return c.SendStatus(http.StatusInternalServerError)
	}

	client := tai.New(ctx, integration)

	// Handle ShipmentUpdate and ShipmentCreate webhooks with the same model
	load := client.TaiShipmentToLoad(body)

	err = loadDB.UpsertLoad(ctx, &load, &integration.Service)
	if err != nil {
		log.Error(
			ctx,
			"error upserting load",
			zap.Error(err), zap.Uint("service_id", userServiceID),
		)

		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.SendStatus(http.StatusOK)
}
