package integrations

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/tms/tai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	customerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
)

// Tai TMS does not support pulling customers from the TMS, so we need to use the webhook to create customers.
// We have a special persistent webhook token (defined in webhook_token.go)
// that is used to authenticate the webhook.
func TaiCustomerWebhookHandler(c *fiber.Ctx) error {
	var body tai.CustomerResp
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext())
	userServiceID := middleware.ServiceIDFromContext(c)

	integration, err := integrationDB.GetByName(ctx, userServiceID, models.Tai)
	if err != nil {
		log.Error(
			ctx,
			"integration not configured properly",
			zap.Error(err), zap.Uint("service_id", userServiceID),
		)

		return c.SendStatus(http.StatusInternalServerError)
	}

	client := tai.New(ctx, integration)

	// Handle ShipmentUpdate and ShipmentCreate webhooks with the same model
	customer := client.TaiCustomerToCustomer(body)
	customers := []models.TMSCustomer{customer}
	err = customerDB.RefreshTMSCustomers(ctx, &customers)
	if err != nil {
		log.Error(
			ctx, "error refreshing tms customers",
			zap.Error(err), zap.Uint("service_id", userServiceID),
		)

		return c.SendStatus(http.StatusInternalServerError)
	}

	// Load and save customer addresses
	customerID, err := strconv.Atoi(customer.ExternalID)
	if err != nil {
		log.Error(
			ctx, "error converting customer ID to int",
			zap.Error(err), zap.String("customer_id", customer.ExternalID),
		)
		return c.SendStatus(http.StatusInternalServerError)
	}

	locations, err := client.LoadCustomerAddresses(ctx, customerID)
	if err != nil {
		log.Error(
			ctx, "error loading customer addresses",
			zap.Error(err), zap.Uint("service_id", userServiceID),
		)

		return c.SendStatus(http.StatusInternalServerError)
	}

	// Deduplicate locations by ExternalTMSID to prevent ON CONFLICT errors
	// The TAI API might return locations with duplicate OrganizationID values or
	// we might receive the same address data multiple times
	locationMap := make(map[string]models.TMSLocation)
	duplicateCount := 0

	for _, loc := range locations {
		key := loc.ExternalTMSID
		if existing, exists := locationMap[key]; exists {
			duplicateCount++
			log.Info(
				ctx,
				"found duplicate location in TAI response, keeping first occurrence",
				zap.String("external_tms_id", key),
				zap.String("existing_name", existing.Name),
				zap.String("existing_address", existing.FullAddress),
				zap.String("duplicate_name", loc.Name),
				zap.String("duplicate_address", loc.FullAddress),
			)
		} else {
			locationMap[key] = loc
		}
	}

	// Convert map back to slice
	dedupedLocations := make([]models.TMSLocation, 0, len(locationMap))
	for _, loc := range locationMap {
		dedupedLocations = append(dedupedLocations, loc)
	}

	if duplicateCount > 0 {
		log.Info(
			ctx,
			"deduplicated locations before database insert",
			zap.Int("original_count", len(locations)),
			zap.Int("deduped_count", len(dedupedLocations)),
			zap.Int("duplicates_removed", duplicateCount),
		)
	}

	locations = dedupedLocations

	err = tmsLocationDB.RefreshTMSLocations(ctx, &locations)
	if err != nil {
		log.Error(
			ctx, "error refreshing tms locations",
			zap.Error(err), zap.Uint("service_id", userServiceID),
		)

		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.SendStatus(http.StatusOK)
}
