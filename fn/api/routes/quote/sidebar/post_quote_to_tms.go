package quoteprivate

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	quoteCommon "github.com/drumkitai/drumkit/fn/api/routes/quote/common"
)

type PostQuoteToTMSBody struct {
	CustomerID            string               `json:"customerId"`
	QuotePrice            int                  `json:"quotePrice"`
	QuoteNumber           string               `json:"quoteNumber"`
	TransportType         models.TransportType `json:"transportType"`
	PickupLocationZip     string               `json:"pickupLocationZip"`
	PickupLocationCity    string               `json:"pickupLocationCity"`
	PickupLocationState   string               `json:"pickupLocationState"`
	PickupDate            models.NullTime      `json:"pickupDate"`
	DeliveryLocationZip   string               `json:"deliveryLocationZip"`
	DeliveryLocationCity  string               `json:"deliveryLocationCity"`
	DeliveryLocationState string               `json:"deliveryLocationState"`
	DeliveryDate          models.NullTime      `json:"deliveryDate"`
	CommodityDescription  string               `json:"commodityDescription"`
	CommodityWeight       string               `json:"commodityWeight"`
}

type PostQuoteToTMSResponse struct {
	QuoteID int `json:"quoteId"`
}

func PostQuoteToTMS(c *fiber.Ctx) error {
	var body PostQuoteToTMSBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	userServiceID := middleware.ServiceIDFromContext(c)
	userID := middleware.UserIDFromContext(c)

	tmsIntegration, err := integrationDB.GetTMSForQuoteByServiceID(ctx, userServiceID)
	if err != nil {
		log.Error(
			ctx,
			"tms-quote integration not configured properly",
			zap.Error(err),
			zap.Uint("service_id", userServiceID),
			zap.Uint("user_id", userID),
			zap.String("integration_name", string(tmsIntegration.Name)),
		)
		return err
	}

	// TMS Quotes are only supported for Tai and Turvo as of now
	if tmsIntegration.Name != models.Turvo && tmsIntegration.Name != models.Tai {
		log.Error(
			ctx,
			"submitting quotes is not supported for this integration",
			zap.Error(err),
			zap.Uint("service_id", userServiceID),
			zap.Uint("user_id", userID),
			zap.String("integration_name", string(tmsIntegration.Name)),
		)

		return err
	}

	// Populate city/state/zip from user input to enabled tiered lane lookups;
	// if error, fail-open and perform only inputted history lookup
	if err := quoteCommon.ValidateLocation(
		ctx,
		&body.PickupLocationCity,
		&body.PickupLocationState,
		&body.PickupLocationZip,
		nil,
	); err != nil {
		log.Warn(ctx, "error validating pickup", zap.Error(err))
	}

	if err := quoteCommon.ValidateLocation(
		ctx,
		&body.DeliveryLocationCity,
		&body.DeliveryLocationState,
		&body.DeliveryLocationZip,
		nil,
	); err != nil {
		log.Warn(ctx, "error validating dropoff", zap.Error(err))
	}

	tmsClient, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	quoteResponse, err := tmsClient.CreateQuote(ctx, models.CreateQuoteBody{
		CustomerID:            body.CustomerID,
		QuotePrice:            body.QuotePrice,
		QuoteNumber:           body.QuoteNumber,
		TransportType:         body.TransportType,
		PickupLocationCity:    body.PickupLocationCity,
		PickupLocationState:   body.PickupLocationState,
		PickupLocationZip:     body.PickupLocationZip,
		PickupDate:            body.PickupDate,
		DeliveryLocationCity:  body.DeliveryLocationCity,
		DeliveryLocationState: body.DeliveryLocationState,
		DeliveryLocationZip:   body.DeliveryLocationZip,
		DeliveryDate:          body.DeliveryDate,
		CommodityDescription:  body.CommodityDescription,
		CommodityWeight:       body.CommodityWeight,
		UserID:                userID,
	})
	if err != nil {
		log.Error(ctx, "error creating quote on TMS", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusCreated).JSON(
		PostQuoteToTMSResponse{
			QuoteID: quoteResponse.QuoteID,
		},
	)
}
