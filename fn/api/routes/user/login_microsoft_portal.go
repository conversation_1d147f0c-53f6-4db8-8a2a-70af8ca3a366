package user

import (
	"errors"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds/user"
)

type (
	LoginMicrosoftPortalBody struct {
		IDToken string `json:"idToken"`
	}

	LoginMicrosoftPortalResponse struct {
		AccessToken     string      `json:"access_token"`
		TokenExpiration int64       `json:"token_expiration"` // In Unix time
		Email           string      `json:"email"`
		Role            models.Role `json:"role"`
		ServiceID       uint        `json:"service_id"`
		TokenType       string      `json:"token_type"`
	}
)

// LoginMicrosoftPortal handles Microsoft SSO login for Drumkit Portal.
func LoginMicrosoftPortal(c *fiber.Ctx) error {
	var body LoginMicrosoftPortalBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext())

	claims, err := validateSSOToken(ctx, body.IDToken)
	if err != nil {
		log.Error(ctx, "invalid microsoft id token", zap.Error(err))
		return c.SendStatus(http.StatusUnauthorized)
	}

	email := claims.PreferredUsername
	user, err := user.GetByEmailForOnboard(ctx, email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.SendStatus(http.StatusUnauthorized)
		}
		return c.SendStatus(http.StatusInternalServerError)
	}

	accessToken, err := CreateDrumkitAccessToken(ctx, user.EmailAddress, user)
	if err != nil {
		log.Error(ctx, "generating drumkit access token failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(LoginMicrosoftPortalResponse{
		AccessToken:     *accessToken,
		Email:           user.EmailAddress,
		Role:            user.Role,
		ServiceID:       user.ServiceID,
		TokenExpiration: time.Now().Add(30 * 24 * time.Hour).Unix(),
		TokenType:       "Bearer",
	})
}
