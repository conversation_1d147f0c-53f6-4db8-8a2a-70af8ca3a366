package user

import (
	"errors"
	"net/http"
	"slices"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/jwt"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/fn/api/env"
)

type (
	LoginMicrosoftFromOnPremBody struct {
		OnPremUser      models.OnPremUser `json:"user"`
		AccessToken     string            `json:"access_token"`
		TokenExpiration int64             `json:"token_expiration"` // In Unix time
		Email           string            `json:"email"`
		TokenType       string            `json:"token_type"`

		// For local dev only: the email can be specified directly
		DevEmail string `json:"dev_email"`
	}

	LoginMicrosoftFromOnPremResponse struct {
		AccessToken     string `json:"access_token"`
		TokenExpiration int64  `json:"token_expiration"` // In Unix time
		Email           string `json:"email"`
		GroupID         uint   `json:"group_id"`
		ServiceID       uint   `json:"service_id"`
		TokenType       string `json:"token_type"`
	}
)

func LoginMicrosoftFromOnPrem(c *fiber.Ctx) error {
	var body LoginMicrosoftFromOnPremBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	email := body.OnPremUser.EmailAddress
	ctx = log.With(ctx, zap.String("userEmail", email))

	user, err := userDB.GetByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error(ctx, "login failed: user does not exist", zap.String("error", err.Error()))
			return c.SendStatus(http.StatusUnauthorized)
		}

		// Some other DB error
		return c.SendStatus(http.StatusInternalServerError)
	}

	accessToken, err := jwt.NewAccessToken(
		user.EmailAddress,
		jwt.WithAppEnv(env.Vars.AppEnv),
		jwt.WithServiceID(&user.ServiceID),
	)
	if err != nil {
		log.Error(ctx, "creating new jwt failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	user.HashedSessionToken = middleware.HashedToken(accessToken)

	if !slices.Contains(user.HashedSessionTokens, user.HashedSessionToken) {
		user.HashedSessionTokens = append(user.HashedSessionTokens, user.HashedSessionToken)
	}

	if err := userDB.Update(ctx, user); err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(LoginMicrosoftFromOnPremResponse{
		AccessToken:     accessToken,
		Email:           user.EmailAddress,
		ServiceID:       user.ServiceID,
		TokenExpiration: time.Now().Add(30 * 24 * time.Hour).Unix(),
		TokenType:       "Bearer",
	})
}
