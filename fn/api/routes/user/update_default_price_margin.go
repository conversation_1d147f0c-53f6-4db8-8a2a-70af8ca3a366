package user

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	UpdatePriceMarginBody struct {
		DefaultPriceMargin     float64                       `json:"defaultPriceMargin" validate:"required"`
		DefaultPriceMarginType models.DefaultPriceMarginType `json:"defaultPriceMarginType" validate:"required"`
	}
)

func UpdateDefaultPriceMargin(c *fiber.Ctx) error {
	var body UpdatePriceMarginBody
	err := api.Parse(c, nil, nil, &body)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	claims := middleware.ClaimsFromContext(c)

	user, err := userDB.GetByEmail(ctx, claims.Email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(fmt.Sprintf("email %s not found", claims.Email))
		}

		log.Error(ctx, "error getting user", zap.String("email", claims.Email), zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	userEmptyDefaultMargins := user.DefaultPriceMargin == nil || user.DefaultPriceMarginType == nil
	userMatchingDefaultMargins := !userEmptyDefaultMargins &&
		body.DefaultPriceMarginType == *user.DefaultPriceMarginType &&
		body.DefaultPriceMargin == *user.DefaultPriceMargin

	if userMatchingDefaultMargins {
		return c.Status(http.StatusOK).SendString("submitted values match existing default price margin")
	}

	if user.DefaultPriceMargin == nil || body.DefaultPriceMargin != *user.DefaultPriceMargin {
		user.DefaultPriceMargin = &body.DefaultPriceMargin
	}

	if user.DefaultPriceMarginType == nil || body.DefaultPriceMarginType != *user.DefaultPriceMarginType {
		user.DefaultPriceMarginType = &body.DefaultPriceMarginType
	}

	if err := userDB.Update(ctx, user); err != nil {
		log.Error(ctx, "could not update user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.SendStatus(http.StatusOK)
}
