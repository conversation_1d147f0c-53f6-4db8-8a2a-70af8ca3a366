package user

import (
	"errors"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/services"
	"github.com/drumkitai/drumkit/fn/api/env"
)

type (
	SignupGoogleFromOnPremBody struct {
		OnPremUser models.OnPremUser `json:"user"`

		// For local dev only: the email can be specified directly
		DevEmail string
	}

	SignupGoogleFromOnPremResponse struct {
		AccessToken     string `json:"access_token"`
		TokenExpiration int64  `json:"token_expiration"` // In Unix time
		Email           string `json:"email"`
		GroupID         uint   `json:"group_id"`
		ServiceID       uint   `json:"service_id"`
		TokenType       string `json:"token_type"`
	}
)

// SignupGoogleFromOnPrem handles a special Gmail signup process where the request comes from a customer who self-hosts
// Drumkit. This process is different from a regular signup as it involves mapping all relevant user information,
// excluding the OAuth tokens, for security purposes. The OAuth tokens themselves are maintained by the customer and are
// not stored by us. Instead, we notify the customer when these tokens are nearing expiration through the `WatchInbox`
// lambda.
func SignupGoogleFromOnPrem(c *fiber.Ctx) error {
	var body SignupGoogleFromOnPremBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.String("emailAddress", body.OnPremUser.EmailAddress))

	if env.Vars.AppEnv == "dev" && body.DevEmail != "" {
		ctx = log.With(ctx, zap.String("userEmail", body.DevEmail))

		err := userDB.Create(ctx, &models.User{
			EmailAddress:       body.DevEmail,
			GmailLastHistoryID: 1,
			Service:            models.Service{},
		})
		if err != nil {
			return c.Status(http.StatusInternalServerError).SendString(err.Error())
		}

		return c.Status(http.StatusCreated).SendString("user created successfully")
	}

	log.Info(ctx, "received google signup request from customer that self-hosts drumkit")

	email := body.OnPremUser.EmailAddress

	// Block signup requests from *@gmail.com, except Google's official oauth test account
	if strings.HasSuffix(email, "@gmail.com") && email != "<EMAIL>" {
		// Request is not from a GSuite account (block gmail.com signups)
		log.Error(ctx, "rejecting signup request from gmail account")

		return c.Status(http.StatusUnauthorized).SendString("gsuite domain required")
	}

	user := models.User{
		EmailAddress:       body.OnPremUser.EmailAddress,
		EmailProvider:      models.GmailEmailProvider,
		OnPremID:           body.OnPremUser.ID,
		IsOnPrem:           true,
		OnPremPortalDomain: body.OnPremUser.PortalDomain,
		Name:               body.OnPremUser.Name,
		TokenExpiry:        body.OnPremUser.TokenExpiry,
		WebhookExpiration:  body.OnPremUser.WebhookExpiration,
	}

	if err := services.AssignOrCreateService(ctx, &user); err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	userFound, err := userDB.GetByEmail(ctx, body.OnPremUser.EmailAddress)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return c.SendStatus(http.StatusInternalServerError)
	}

	user.ID = userFound.ID

	if userFound.ID == 0 {
		err = userDB.Create(ctx, &user)
	} else {
		err = userDB.Update(ctx, user)
	}

	if err != nil {
		log.Error(ctx, "error creating/updating user in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	accessToken, err := CreateDrumkitAccessToken(ctx, email, user)
	if err != nil {
		log.Error(ctx, "generating drumkit access token failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusCreated).JSON(LoginGoogleFromOnPremResponse{
		AccessToken:     *accessToken,
		Email:           email,
		ServiceID:       user.ServiceID,
		TokenExpiration: time.Now().Add(30 * 24 * time.Hour).Unix(),
		TokenType:       "Bearer",
	})
}
