package user

import (
	"errors"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/services"
	"github.com/drumkitai/drumkit/fn/api/env"
)

type (
	SignupMicrosoftFromOnPremBody struct {
		OnPremUser models.OnPremUser `json:"user"`

		// For local dev only: the email can be specified directly
		DevEmail string `json:"dev_email"`
	}

	SignupMicrosoftFromOnPremResponse struct {
		AccessToken     string `json:"access_token"`
		TokenExpiration int64  `json:"token_expiration"` // In Unix time
		Email           string `json:"email"`
		GroupID         uint   `json:"group_id"`
		ServiceID       uint   `json:"service_id"`
		TokenType       string `json:"token_type"`
	}
)

// SignupMicrosoftFromOnPrem handles a special Outlook signup process where the request comes from a customer who
// self-hosts Drumkit. This process is different from a regular signup as it involves mapping all relevant user
// information, excluding the OAuth tokens, for security purposes. The OAuth tokens themselves are maintained by the
// customer and are not stored by us. Instead, we notify the customer when these tokens are nearing expiration through
// the `WatchInbox` lambda.
func SignupMicrosoftFromOnPrem(c *fiber.Ctx) error {
	var body SignupMicrosoftFromOnPremBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("emailAddress", body.OnPremUser.EmailAddress))

	if env.Vars.AppEnv == "dev" && body.DevEmail != "" {
		err := userDB.Create(ctx, &models.User{
			EmailAddress:  body.DevEmail,
			EmailProvider: models.OutlookEmailProvider,
			Service:       models.Service{},
		})
		if err != nil {
			return c.Status(http.StatusInternalServerError).SendString(err.Error())
		}

		return c.Status(http.StatusCreated).SendString("dev user created successfully")
	}

	log.Info(ctx, "received microsoft signup request from customer that self-hosts drumkit")

	user := &models.User{
		EmailAddress:       strings.ToLower(body.OnPremUser.EmailAddress),
		EmailProvider:      models.OutlookEmailProvider,
		OnPremID:           body.OnPremUser.ID,
		IsOnPrem:           true,
		OnPremPortalDomain: body.OnPremUser.PortalDomain,
		MailClientID:       body.OnPremUser.MailClientID,
		Name:               body.OnPremUser.Name,
		OutlookClientState: uuid.NewString(),
		TenantID:           body.OnPremUser.TenantID,
		TokenExpiry:        body.OnPremUser.TokenExpiry,
		WebhookExpiration:  body.OnPremUser.WebhookExpiration,
	}

	switch {
	case strings.HasSuffix(body.OnPremUser.EmailAddress, "@axleapi.com") ||
		strings.HasSuffix(body.OnPremUser.EmailAddress, "@drumkit.ai"):

		user.ServiceID = 1
	default:
		if err := services.AssignOrCreateService(ctx, user); err != nil {
			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	existingUser, err := userDB.GetByEmailForOnboard(ctx, body.OnPremUser.EmailAddress)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return c.SendStatus(http.StatusInternalServerError)
	}

	user.ID = existingUser.ID
	user.Role = existingUser.Role

	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = userDB.Create(ctx, user)
	} else {
		// Preserving existing user's session tokens
		user.EmailAddress = existingUser.EmailAddress
		user.HashedSessionToken = existingUser.HashedSessionToken
		user.HashedSessionTokens = existingUser.HashedSessionTokens

		err = userDB.Update(ctx, *user)
	}

	if err != nil {
		log.Error(ctx, "error creating/updating user in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	accessToken, err := CreateDrumkitAccessToken(ctx, user.EmailAddress, *user)
	if err != nil {
		log.Error(ctx, "generating drumkit access token failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusCreated).JSON(SignupMicrosoftFromOnPremResponse{
		AccessToken:     *accessToken,
		Email:           user.EmailAddress,
		ServiceID:       user.ServiceID,
		TokenExpiration: time.Now().Add(30 * 24 * time.Hour).Unix(),
		TokenType:       "Bearer",
	})
}
