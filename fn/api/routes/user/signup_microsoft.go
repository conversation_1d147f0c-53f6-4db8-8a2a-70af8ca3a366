package user

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/services"
	"github.com/drumkitai/drumkit/fn/api/env"
	helpers "github.com/drumkitai/drumkit/fn/ingestion"
)

type (
	SignupMicrosoftBody struct {
		msclient.MicrosoftAuthCodeRequest

		// For local dev only: the email can be specified directly
		DevEmail string
	}

	SignupMicrosoftResponse struct {
		AccessToken     string      `json:"access_token"`
		TokenExpiration int64       `json:"token_expiration"` // In Unix time
		Email           string      `json:"email"`
		Role            models.Role `json:"role"`
		GroupID         uint        `json:"group_id"`
		ServiceID       uint        `json:"service_id"`
		TokenType       string      `json:"token_type"`
	}
)

func SignupMicrosoft(c *fiber.Ctx) error {
	var body SignupMicrosoftBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	// Log the account name/email (does not include the access token)
	ctx := log.With(c.UserContext(), zap.Any("account", body.Account))

	if env.Vars.AppEnv == "dev" && body.DevEmail != "" {
		return signupMicrosoftDev(ctx, c, &body)
	}

	log.Info(ctx, "received microsoft signup request")

	user, err := handleMicrosoftAuth(ctx, body.MicrosoftAuthCodeRequest)
	if err != nil {
		log.Error(ctx, "microsoft auth failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	existingUser, err := userDB.GetByEmailForOnboard(ctx, body.Account.Username)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return c.SendStatus(http.StatusInternalServerError)
	}

	user.ID = existingUser.ID
	user.Role = existingUser.Role

	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = userDB.Create(ctx, user)
	} else {
		// Preserve existing user's session tokens
		user.HashedSessionToken = existingUser.HashedSessionToken
		user.HashedSessionTokens = existingUser.HashedSessionTokens

		// Preserve existing user secondary email address - primary is always the one used for sign-in/sign-up
		if existingUser.SecondaryEmailAddress != "" {
			user.SecondaryEmailAddress = existingUser.SecondaryEmailAddress
		}

		err = userDB.Update(ctx, *user)
	}

	if err != nil {
		log.Error(ctx, "error creating/updating user in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	accessToken, err := CreateDrumkitAccessToken(ctx, user.EmailAddress, *user)
	if err != nil {
		log.Error(ctx, "generating drumkit access token failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Backfill new user emails.
	if err := helpers.EnqueueBackfillJob(
		ctx,
		SQSClient,
		env.Vars.BackfillSQSQueueURL,
		"outlook",
		user,
		helpers.WithBackfillHours(env.Vars.BackfillHours),
	); err != nil {
		log.Error(ctx, "error enqueuing backfill job", zap.Error(err))
	}

	return c.Status(http.StatusCreated).JSON(SignupMicrosoftResponse{
		AccessToken:     *accessToken,
		Email:           user.EmailAddress,
		Role:            user.Role,
		ServiceID:       user.ServiceID,
		TokenExpiration: time.Now().Add(30 * 24 * time.Hour).Unix(),
		TokenType:       "Bearer",
	})
}

func handleMicrosoftAuth(ctx context.Context, auth msclient.MicrosoftAuthCodeRequest) (*models.User, error) {
	user := &models.User{
		EmailAddress:       strings.ToLower(auth.Account.Username),
		EmailProvider:      models.OutlookEmailProvider,
		Name:               auth.Account.Name,
		MailClientID:       auth.Account.LocalAccountID,
		TenantID:           auth.Account.TenantID,
		OutlookClientState: uuid.NewString(),
	}

	if err := services.AssignOrCreateService(ctx, user); err != nil {
		return nil, err
	}

	tokenResp, err := msclient.GetOboToken(ctx, auth, env.Vars.MicrosoftClientID, env.Vars.MicrosoftClientSecret)
	if err != nil {
		return user, fmt.Errorf("error getting OBO token: %w", err)
	}

	user.EncryptedAccessToken, user.EncryptedRefreshToken, err = crypto.EncryptTokens(
		ctx,
		tokenResp.AccessToken,
		tokenResp.RefreshToken,
		nil,
	)
	if err != nil {
		return user, fmt.Errorf("outlook token encryption error: %w", err)
	}

	user.TokenExpiry = time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second)

	return user, watchOutlookInbox(ctx, user)
}

// Creates an outlook subscription for new messages (and updates the user with the subscription details)
func watchOutlookInbox(ctx context.Context, user *models.User) error {
	client, err := msclient.New(ctx, env.Vars.MicrosoftClientID, env.Vars.MicrosoftClientSecret, user)
	if err != nil {
		return fmt.Errorf("error building MSClient: %w", err)
	}

	sub, err := client.WatchInbox(ctx, env.Vars.MicrosoftWebhookURL, user.OutlookClientState)
	if err != nil {
		return err
	}

	user.WebhookExpiration = sub.ExpirationDateTime
	user.OutlookSubscriptionID = sub.ID

	log.Info(ctx, "successfully created outlook subscription", zap.String("subId", user.OutlookSubscriptionID))

	return nil
}

func signupMicrosoftDev(ctx context.Context, c *fiber.Ctx, body *SignupMicrosoftBody) error {
	ctx = log.With(ctx, zap.String("userEmail", body.DevEmail))

	user, err := userDB.GetByEmail(ctx, body.DevEmail)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return c.SendStatus(http.StatusInternalServerError)
	}

	// If user doesn't exist, user will be a new empty struct
	// and we can populate it with the dev email.
	if user.ID == 0 {
		user.EmailAddress = body.DevEmail
		user.EmailProvider = models.OutlookEmailProvider
	}

	if err := services.AssignOrCreateService(ctx, &user); err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	if user.ID == 0 {
		err = userDB.Create(ctx, &user)
	} else {
		err = userDB.Update(ctx, user)
	}
	if err != nil {
		return c.Status(http.StatusInternalServerError).SendString(err.Error())
	}

	if err := helpers.EnqueueBackfillJob(
		ctx,
		SQSClient,
		env.Vars.BackfillSQSQueueURL,
		"outlook",
		&user,
		helpers.WithBackfillHours(env.Vars.BackfillHours),
	); err != nil {
		log.Error(ctx, "error enqueuing backfill job for dev user", zap.Error(err))
	}

	return c.Status(http.StatusCreated).SendString("dev user created successfully")
}
