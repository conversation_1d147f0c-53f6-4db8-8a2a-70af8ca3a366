package user

import (
	"context"
	"errors"
	"net/http"
	"slices"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/jwt"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	userGroupsDB "github.com/drumkitai/drumkit/common/rds/usergroups"
	"github.com/drumkitai/drumkit/fn/api/env"
)

type (
	LoginGoogleBody struct {
		api.GoogleAuthCodeRequest

		// For local dev only: the email can be specified directly
		DevEmail string
	}

	LoginGoogleResponse struct {
		AccessToken     string      `json:"access_token"`
		TokenExpiration int64       `json:"token_expiration"` // In Unix time
		Email           string      `json:"email"`
		Role            models.Role `json:"role"`
		GroupID         uint        `json:"group_id"`
		ServiceID       uint        `json:"service_id"`
		TokenType       string      `json:"token_type"`
	}
)

func LoginGoogle(c *fiber.Ctx) error {
	var body LoginGoogleBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	var email string
	var resp api.GoogleAuthResponse
	var err error

	// If manual POST request and not from drumkit-portal, require only dev email
	if env.Vars.AppEnv == "dev" && body.Scope == "" {
		if email = body.DevEmail; email == "" {
			return c.Status(http.StatusBadRequest).SendString("email is required")
		}
	} else {
		// get email from Google via auth code
		resp, err = api.CallBackFromGoogle(ctx, body.GoogleAuthCodeRequest)
		if err != nil {
			log.Error(ctx, "callBackFromGoogle failed", zap.String("error", err.Error()))
			return c.SendStatus(http.StatusUnauthorized)
		}

		email = resp.UserInfo.Email
	}

	ctx = log.With(ctx, zap.String("userEmail", email))

	user, err := userDB.GetByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error(ctx, "login failed: user does not exist", zap.String("error", err.Error()))
			return c.SendStatus(http.StatusUnauthorized)
		}

		// Some other DB error
		return c.SendStatus(http.StatusInternalServerError)
	}

	// NOTE: that dev mode can either be a dry-run with no Google auth (see README) or
	// localhost drumkit-portal that *does* use Google auth
	if env.Vars.AppEnv == "dev" || body.DevEmail != "" {
		user.EncryptedAccessToken, user.EncryptedRefreshToken, err = crypto.EncryptTokens(
			ctx,
			resp.AccessToken,
			resp.RefreshToken,
			nil,
		)
		if err != nil {
			log.Error(ctx, "token encryption failed", zap.Error(err))

			return c.SendStatus(http.StatusInternalServerError)
		}

		user.TokenExpiry = resp.ExpTime
		user.MailClientID = resp.UserInfo.ID

		// Update user with gmail tokens
		if err := userDB.Update(ctx, user); err != nil {
			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	// Re-watch inbox in case user is logging because their account went stale.
	// NOTE: We don't re-watch on-prem users because they don't have Gmail/Outlook tokens; The `WatchInbox` Lambda
	// pings on-prem hosts to re-subscribe to the user's inbox & refresh tokens.
	if !user.IsOnPrem {
		if err := watchGmailInbox(ctx, &user); err != nil {
			// Fail-open. If initial watch fails, manually trigger Lambda for the user
			log.Error(ctx, "watchGmailInbox failed", zap.Error(err))
		}
	}

	user.Name = resp.UserInfo.Name
	accessToken, err := CreateDrumkitAccessToken(ctx, email, user)
	if err != nil {
		log.Error(ctx, "generating drumkit access token failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	userGroup, err := userGroupsDB.GetByUserID(ctx, user.ID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error(ctx, "error while getting user group", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(LoginGoogleResponse{
		AccessToken:     *accessToken,
		Email:           email,
		Role:            user.Role,
		GroupID:         userGroup.ID,
		ServiceID:       user.ServiceID,
		TokenExpiration: time.Now().Add(30 * 24 * time.Hour).Unix(),
		TokenType:       "Bearer",
	})
}

func CreateDrumkitAccessToken(ctx context.Context, email string, user models.User) (*string, error) {
	generatedAccessToken, err := jwt.NewAccessToken(
		email,
		jwt.WithJWT(env.Vars.JWT),
		jwt.WithAppEnv(env.Vars.AppEnv),
		jwt.WithServiceID(&user.ServiceID),
	)
	if err != nil {
		log.Error(ctx, "creating new jwt failed", zap.Error(err))
		return nil, err
	}

	user.HashedSessionToken = middleware.HashedToken(generatedAccessToken)

	// add hashed session token to list of all session tokens, if it isn't already there
	if !slices.Contains(user.HashedSessionTokens, user.HashedSessionToken) {
		user.HashedSessionTokens = append(user.HashedSessionTokens, user.HashedSessionToken)
	}

	if err := userDB.Update(ctx, user); err != nil {
		return nil, err
	}

	return &generatedAccessToken, nil
}
