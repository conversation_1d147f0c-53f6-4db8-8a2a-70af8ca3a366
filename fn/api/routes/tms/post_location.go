package tmsroutes

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/tms/mcleodenterprise"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
)

type (
	CreateLocationBody struct {
		Location LocationCore `json:"location"`
		TMSID    uint         `json:"tmsID"`
	}

	LocationCore struct {
		models.CompanyCoreInfo
		ApptRequired bool `json:"apptRequired"`
		IsShipper    bool `json:"isShipper"`
		IsConsignee  bool `json:"isConsignee"`
	}
	CreateLocationResponse struct {
		Location models.TMSLocation `json:"location"`
		Message  string             `json:"message"`
	}
)

// Only available for Mcleod Enterprise; add to TMSInterface if we add more TMSes
func CreateLocation(c *fiber.Ctx) error {
	var body CreateLocationBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Uint("tmsID", body.TMSID))
	userServiceID := middleware.ServiceIDFromContext(c)

	tmsIntegration, err := integrationDB.Get(ctx, body.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(
				ctx,
				"trying to fetch customers for a service with no active TMS",
				zap.Uint("serviceID", userServiceID),
			)

			return c.SendStatus(http.StatusNotFound)
		}

		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	if tmsIntegration.Name != models.McleodEnterprise {
		log.Error(
			ctx,
			"CreateLocation not implemented for TMS",
			zap.String("tmsName", string(tmsIntegration.Name)),
		)

		return c.SendStatus(http.StatusNotImplemented)
	}

	client, err := mcleodenterprise.New(tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	newLoc := models.TMSLocation{
		TMSIntegrationID: body.TMSID,
		CompanyCoreInfo:  body.Location.CompanyCoreInfo,
		ApptRequired:     body.Location.ApptRequired,
		IsShipper:        body.Location.IsShipper,
		IsConsignee:      body.Location.IsConsignee,
	}
	location, err := client.CreateLocation(ctx, newLoc)
	if err != nil {
		log.Error(ctx, "error creating TMS location", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if err = tmsLocationDB.Create(ctx, location); err != nil {
		// Fail-open; subsequent Mcleod requests with new location will still work
		// Next DB  refresh will add the new location to DB
		log.WarnNoSentry(ctx, "error creating TMS location in DB", zap.Error(err))
	}

	return c.Status(http.StatusCreated).JSON(
		CreateLocationResponse{
			Location: location,
			Message:  "Successfully created TMS location",
		},
	)
}
