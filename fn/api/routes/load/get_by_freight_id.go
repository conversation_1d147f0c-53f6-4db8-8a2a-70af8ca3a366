package load

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/integrations/tms/aljex"
	"github.com/drumkitai/drumkit/common/integrations/tms/relay"
	"github.com/drumkitai/drumkit/common/integrations/tms/turvo"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/rds/generatedemails"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	loadviewDB "github.com/drumkitai/drumkit/common/rds/loadview"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	GetLoadPath struct {
		FreightTrackingID string `params:"freightTrackingID" validate:"required"`
	}

	GetLoadResponse struct {
		TMSName             models.IntegrationName     `json:"tmsName"`
		TMSID               uint                       `json:"tmsID"`
		Load                models.Load                `json:"load"`
		LoadAttributes      models.LoadAttributes      `json:"loadAttributes"`
		ExternalLinks       ExternalLinks              `json:"externalLinks"`
		PendingOutboxEmails models.PendingOutboxEmails `json:"pendingOutboxEmails,omitempty"`
		PickupWarehouse     models.WarehouseCore       `json:"pickupWarehouse,omitempty"`
		DropoffWarehouse    models.WarehouseCore       `json:"dropoffWarehouse,omitempty"`
	}

	ExternalLinks struct {
		ViewCarrier string `json:"viewCarrier"`
	}
)

func GetLoadByFreightID(c *fiber.Ctx) error {
	var path GetLoadPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path))
	userServiceID := middleware.ServiceIDFromContext(c)

	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var tmsIntegration *models.Integration
	// looking up load on DB to get TMS Model
	loadFromDB, dbLoadErr := loadDB.GetLoadByFreightIDAndService(ctx, userServiceID, path.FreightTrackingID)
	if dbLoadErr != nil || loadFromDB.TMS.ID == 0 {
		log.WarnNoSentry(
			ctx,
			"error getting load from DB, falling back to matching by freightTrackingID regex",
			zap.Error(dbLoadErr),
		)

		// fallback: if load is not on DB, match TMS by FreightID
		tmsIntegration, err = integrationDB.MatchTMSByServiceAndFreightID(
			ctx,
			userServiceID,
			path.FreightTrackingID,
		)
		if err != nil {
			log.Error(ctx, "could not get tms integration:", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		// Users occasionally look up Relay loads by the Load# instead of the BookingID, so if
		// BookingID fails we try the Load# lookup to get the load's matched warehouses.
		if tmsIntegration.Name == models.Relay {
			loadFromDB, dbLoadErr = loadDB.GetLoadByExternalTMSIDAndTMSID(
				ctx,
				tmsIntegration.ID,
				path.FreightTrackingID,
			)
			if dbLoadErr != nil {
				log.Info(ctx, "relay lookup failed for both bookingID and load#:", zap.Error(dbLoadErr))
			}
		}
	} else {
		tmsIntegration = &loadFromDB.TMS
	}

	log.With(ctx, zap.String("tmsName", string(tmsIntegration.Name)))

	// We don't care the load was not found or any error from db now
	// We'll try to to fetch the load from the TMS anyway.
	var links ExternalLinks
	var fetchLoadErr error

	refreshedLoad, attributes, fetchLoadErr := getLoadFromTMS(ctx, *tmsIntegration, path.FreightTrackingID)

	if tmsIntegration.Name == models.Turvo {
		links = getTurvoExternalLinks(tmsIntegration.Tenant, refreshedLoad)
	}

	if fetchLoadErr == nil {
		if err = loadDB.UpsertLoad(ctx, &refreshedLoad, &service); err != nil {
			// Fail-open
			log.Error(ctx, "error updating load in DB", zap.Error(err))
		}

		if !perms.HasLoadReadPermissions(c, refreshedLoad) {
			err := fmt.Errorf("not allowed to read load %s", path.FreightTrackingID)
			return c.Status(http.StatusForbidden).SendString(err.Error())
		}

		if err := createLoadViewEvent(ctx, c, &refreshedLoad); err != nil {
			log.Warn(ctx, "error creating load view event", zap.Error(err))
		}

		pendingOutboxEmails, err := generatedemails.GetEmailsByLoadID(ctx, service.ID, refreshedLoad.ID)
		if err != nil {
			// NOTE: fail-open
			log.Error(ctx, "generatedemails query error", zap.Error(err))
		}

		result := GetLoadResponse{
			TMSID:               tmsIntegration.ID,
			TMSName:             tmsIntegration.Name,
			Load:                refreshedLoad,
			LoadAttributes:      attributes,
			ExternalLinks:       links,
			PendingOutboxEmails: *pendingOutboxEmails,
			PickupWarehouse:     apiutil.GetWarehouseCoreFromWarehouse(refreshedLoad.PickupWarehouse),
			DropoffWarehouse:    apiutil.GetWarehouseCoreFromWarehouse(refreshedLoad.DropoffWarehouse),
		}

		return c.Status(http.StatusOK).JSON(result)
	}

	switch {
	case errtypes.IsEntityNotFoundError(fetchLoadErr):
		log.WarnNoSentry(ctx, "load does not exist in TMS", zap.Error(fetchLoadErr))
		return c.SendStatus(http.StatusNotFound)

	case errtypes.IsNotImplementedError(fetchLoadErr):
		log.Info(ctx, fetchLoadErr.Error())

	default:
		log.Warn(ctx, "could not get load from TMS - falling back to DB", zap.Error(fetchLoadErr))
	}

	// Proceeding with Load from DB
	if dbLoadErr != nil {
		if errors.Is(dbLoadErr, gorm.ErrRecordNotFound) {
			return c.SendStatus(http.StatusNotFound)
		}
		return c.SendStatus(http.StatusInternalServerError)
	}

	if !perms.HasLoadReadPermissions(c, loadFromDB) {
		err := fmt.Errorf("not allowed to read load %d", loadFromDB.ID)
		return c.Status(http.StatusForbidden).SendString(err.Error())
	}

	if err := createLoadViewEvent(ctx, c, &refreshedLoad); err != nil {
		log.Warn(ctx, "error creating load view event", zap.Error(err))
	}

	pendingOutboxEmails, err := generatedemails.GetEmailsByLoadID(ctx, service.ID, loadFromDB.ID)
	if err != nil {
		// NOTE: fail-open
		log.Error(ctx, "generatedemails query error", zap.Error(err))
	}

	result := GetLoadResponse{
		TMSID:               tmsIntegration.ID,
		TMSName:             tmsIntegration.Name,
		Load:                loadFromDB,
		LoadAttributes:      attributes,
		ExternalLinks:       links,
		PendingOutboxEmails: *pendingOutboxEmails,
		PickupWarehouse:     apiutil.GetWarehouseCoreFromWarehouse(loadFromDB.PickupWarehouse),
		DropoffWarehouse:    apiutil.GetWarehouseCoreFromWarehouse(loadFromDB.DropoffWarehouse),
	}

	return c.Status(http.StatusOK).JSON(result)
}

func getLoadFromTMS(ctx context.Context,
	integration models.Integration,
	freightID string,
) (models.Load, models.LoadAttributes, error) {

	if integration.Disabled {
		return models.Load{}, models.LoadAttributes{},
			fmt.Errorf("%s TMS ID %d is disabled", integration.Name, integration.ID)
	}

	client, err := tms.New(ctx, integration)
	if err != nil {
		return models.Load{}, models.LoadAttributes{}, fmt.Errorf("error building TMS client: %w", err)
	}

	// First try to get load by freightID (default)
	load, attrs, err := client.GetLoad(ctx, freightID)
	if err != nil {
		log.WarnNoSentry(ctx, "error getting load from TMS, falling back to GetLoadByIDType", zap.Error(err))
		// If we got an error, try searching by PO number. FE fetch load hook expects a single load;
		// user can use advanced search to get multiple loads.
		// NOTE: Processor's placeholder load logic (../../../processor/main.go:230-235) depends
		// on API automatically looking up by multiple ID types if first lookup fails.
		//
		// TODO: Support any ID type via TMS's ID regex patterns as we do in McleodEnterprise, so
		// this handler can be more flexible
		switch integration.Name {
		case models.Aljex:
			loads, attrs, err := client.GetLoadsByIDType(ctx, freightID, aljex.CustomerRefIDType)
			if err == nil && len(loads) > 0 {
				return loads[0], attrs, nil
			}

			return models.Load{}, attrs, err

		case models.McleodEnterprise:
			loads, attrs, err := client.GetLoadsByIDType(ctx, freightID, "")
			if err == nil && len(loads) > 0 {
				return loads[0], attrs, nil
			}

			return models.Load{}, attrs, err

		case models.Relay:
			loads, attrs, err := client.GetLoadsByIDType(ctx, freightID, relay.BookingID)
			if err == nil && len(loads) > 0 {
				return loads[0], attrs, nil
			}

			return models.Load{}, attrs, err

		case models.Turvo:
			loads, attrs, err := client.GetLoadsByIDType(ctx, freightID, turvo.PONumIDType)
			if err == nil && len(loads) > 0 {
				return loads[0], attrs, nil
			}

			return models.Load{}, attrs, err
		}
	}

	return load, attrs, err
}

func getTurvoExternalLinks(tenant string, load models.Load) ExternalLinks {
	return ExternalLinks{
		ViewCarrier: fmt.Sprintf(
			"https://app.turvo.com/#/%s/shipments/%s/details?ae=carrier:%s",
			tenant,
			load.ExternalTMSID,
			load.Carrier.ExternalTMSID,
		),
	}
}

// Create a load view event for the load.
//
// context.Context is required by the DB function and for tracing, logging.
//
// fiber.Ctx param is used to capture the user's metadata.
func createLoadViewEvent(ctx context.Context, fiberCtx *fiber.Ctx, load *models.Load) error {
	claims := middleware.ClaimsFromContext(fiberCtx)
	serviceID := middleware.ServiceIDFromContext(fiberCtx)
	userID := middleware.UserIDFromContext(fiberCtx)

	event := &models.LoadViewEvent{
		UserID:                userID,
		UserEmail:             claims.Email,
		ServiceID:             serviceID,
		LoadID:                load.ID,
		LoadFreightTrackingID: load.FreightTrackingID,
		LoadExternalTMSID:     load.ExternalTMSID,
		TMSID:                 load.TMSID,
	}

	return loadviewDB.CreateEvent(ctx, event)
}
