package tmscustomerschedulingintegration

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsCustomerSchedulingIntegrationDB "github.com/drumkitai/drumkit/common/rds/tms_customer_scheduling_integration"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
)

// GetCustomerAssociations returns all scheduling integration associations for a TMS customer
func GetCustomerAssociations(c *fiber.Ctx) error {
	ctx := c.UserContext()
	userID := middleware.UserIDFromContext(c)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "GetUserByID failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	tmsCustomerIDStr := c.Params("tmsCustomerID")
	tmsCustomerID, err := strconv.ParseUint(tmsCustomerIDStr, 10, 32)
	if err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid TMS customer ID",
		})
	}

	// Verify the TMS customer belongs to the user's service
	tmsCustomer, err := tmsCustomerDB.GetByID(ctx, uint(tmsCustomerID))
	if err != nil {
		log.Error(ctx, "failed to get TMS customer", zap.Error(err))
		return c.Status(http.StatusNotFound).JSON(fiber.Map{
			"error": "TMS customer not found",
		})
	}

	// Verify the TMS customer's integration belongs to the user's service
	integration, err := integrationDB.Get(ctx, tmsCustomer.TMSIntegrationID)
	if err != nil || integration.ServiceID != user.ServiceID {
		return c.Status(http.StatusForbidden).JSON(fiber.Map{
			"error": "Access denied",
		})
	}

	associations, err := tmsCustomerSchedulingIntegrationDB.GetAllAssociationsForCustomer(ctx, uint(tmsCustomerID))
	if err != nil {
		log.Error(ctx, "failed to get customer associations", zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get associations",
		})
	}

	return c.JSON(fiber.Map{
		"tmsCustomer":  tmsCustomer,
		"associations": associations,
	})
}

// SetPreferredIntegration sets a scheduling integration as preferred for a TMS customer
func SetPreferredIntegration(c *fiber.Ctx) error {
	ctx := c.UserContext()
	userID := middleware.UserIDFromContext(c)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "GetUserByID failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	tmsCustomerIDStr := c.Params("tmsCustomerID")
	tmsCustomerID, err := strconv.ParseUint(tmsCustomerIDStr, 10, 32)
	if err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid TMS customer ID",
		})
	}

	var body struct {
		SchedulingIntegrationID uint   `json:"schedulingIntegrationID" validate:"required"`
		Notes                   string `json:"notes,omitempty"`
	}

	if err := c.BodyParser(&body); err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Verify the TMS customer belongs to the user's service
	tmsCustomer, err := tmsCustomerDB.GetByID(ctx, uint(tmsCustomerID))
	if err != nil {
		log.Error(ctx, "failed to get TMS customer", zap.Error(err))
		return c.Status(http.StatusNotFound).JSON(fiber.Map{
			"error": "TMS customer not found",
		})
	}

	// Verify the TMS customer's integration belongs to the user's service
	tmsIntegration, err := integrationDB.Get(ctx, tmsCustomer.TMSIntegrationID)
	if err != nil || tmsIntegration.ServiceID != user.ServiceID {
		return c.Status(http.StatusForbidden).JSON(fiber.Map{
			"error": "Access denied",
		})
	}

	// Verify the scheduling integration belongs to the user's service
	schedulingIntegration, err := integrationDB.Get(ctx, body.SchedulingIntegrationID)
	if err != nil || schedulingIntegration.ServiceID != user.ServiceID {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid scheduling integration",
		})
	}

	// Set as preferred
	err = tmsCustomerSchedulingIntegrationDB.SetPreferred(ctx, uint(tmsCustomerID), body.SchedulingIntegrationID, user.ID)
	if err != nil {
		log.Error(ctx, "failed to set preferred integration", zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to set preferred integration",
		})
	}

	log.Info(ctx, "set preferred scheduling integration for customer",
		zap.Uint("tmsCustomerID", uint(tmsCustomerID)),
		zap.String("customerName", tmsCustomer.Name),
		zap.Uint("schedulingIntegrationID", body.SchedulingIntegrationID),
		zap.String("schedulingIntegrationName", string(schedulingIntegration.Name)))

	return c.JSON(fiber.Map{
		"message": "Preferred integration set successfully",
	})
}

// GetRecommendedIntegration returns the recommended scheduling integration for a TMS customer
func GetRecommendedIntegration(c *fiber.Ctx) error {
	ctx := c.UserContext()
	userID := middleware.UserIDFromContext(c)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "GetUserByID failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	tmsCustomerIDStr := c.Params("tmsCustomerID")
	tmsCustomerID, err := strconv.ParseUint(tmsCustomerIDStr, 10, 32)
	if err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid TMS customer ID",
		})
	}

	// Verify the TMS customer belongs to the user's service
	tmsCustomer, err := tmsCustomerDB.GetByID(ctx, uint(tmsCustomerID))
	if err != nil {
		log.Error(ctx, "failed to get TMS customer", zap.Error(err))
		return c.Status(http.StatusNotFound).JSON(fiber.Map{
			"error": "TMS customer not found",
		})
	}

	// Verify the TMS customer's integration belongs to the user's service
	integration, err := integrationDB.Get(ctx, tmsCustomer.TMSIntegrationID)
	if err != nil || integration.ServiceID != user.ServiceID {
		return c.Status(http.StatusForbidden).JSON(fiber.Map{
			"error": "Access denied",
		})
	}

	recommendedIntegration, err := tmsCustomerSchedulingIntegrationDB.GetRecommendedIntegrationForCustomer(ctx, uint(tmsCustomerID))
	if err != nil {
		log.Error(ctx, "failed to get recommended integration", zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get recommended integration",
		})
	}

	if recommendedIntegration == nil {
		return c.JSON(fiber.Map{
			"tmsCustomer":            tmsCustomer,
			"recommendedIntegration": nil,
			"message":                "No preferred integration found for this customer",
		})
	}

	return c.JSON(fiber.Map{
		"tmsCustomer":            tmsCustomer,
		"recommendedIntegration": recommendedIntegration,
	})
}

// DeleteAssociation removes an association between a TMS customer and scheduling integration
func DeleteAssociation(c *fiber.Ctx) error {
	ctx := c.UserContext()
	userID := middleware.UserIDFromContext(c)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "GetUserByID failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	tmsCustomerIDStr := c.Params("tmsCustomerID")
	tmsCustomerID, err := strconv.ParseUint(tmsCustomerIDStr, 10, 32)
	if err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid TMS customer ID",
		})
	}

	schedulingIntegrationIDStr := c.Params("schedulingIntegrationID")
	schedulingIntegrationID, err := strconv.ParseUint(schedulingIntegrationIDStr, 10, 32)
	if err != nil {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid scheduling integration ID",
		})
	}

	// Verify the TMS customer belongs to the user's service
	tmsCustomer, err := tmsCustomerDB.GetByID(ctx, uint(tmsCustomerID))
	if err != nil {
		log.Error(ctx, "failed to get TMS customer", zap.Error(err))
		return c.Status(http.StatusNotFound).JSON(fiber.Map{
			"error": "TMS customer not found",
		})
	}

	// Verify the TMS customer's integration belongs to the user's service
	integration, err := integrationDB.Get(ctx, tmsCustomer.TMSIntegrationID)
	if err != nil || integration.ServiceID != user.ServiceID {
		return c.Status(http.StatusForbidden).JSON(fiber.Map{
			"error": "Access denied",
		})
	}

	err = tmsCustomerSchedulingIntegrationDB.DeleteAssociation(ctx, uint(tmsCustomerID), uint(schedulingIntegrationID))
	if err != nil {
		log.Error(ctx, "failed to delete association", zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to delete association",
		})
	}

	return c.JSON(fiber.Map{
		"message": "Association deleted successfully",
	})
}
