package metabase

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/fn/api/env"
)

type (
	DashboardResponse struct {
		ID          uint   `json:"id"`
		URL         string `json:"url"`
		QuestionID  int    `json:"questionId"`
		DashboardID int    `json:"dashboardId"`
		UUID        string `json:"uuid"`
		Name        string `json:"name"`
		Description string `json:"description"`
		ServiceID   uint   `json:"serviceId"`
		UserID      *uint  `json:"userId,omitempty"`
		CreatedAt   string `json:"createdAt"`
		UpdatedAt   string `json:"updatedAt"`
		IFrameURL   string `json:"iframeUrl"`
	}

	DashboardsResponse struct {
		Dashboards []DashboardResponse `json:"dashboards"`
	}

	DateRange struct {
		From string `json:"from"`
		To   string `json:"to"`
	}

	ViewType string

	View struct {
		Type ViewType
		ID   int
	}
)

const (
	Question  ViewType = "question"
	Dashboard ViewType = "dashboard"

	tokenExpiry = 10 * time.Minute
)

func isDrumkitAdmin(email string) error {
	if !strings.HasSuffix(email, "@drumkit.ai") {
		return errors.New("only drumkit admins allowed")
	}

	return nil
}

// newView creates a View if either ID is valid
func newView(questionID, dashboardID int) (View, error) {
	switch {
	case questionID != 0:
		return View{Type: Question, ID: questionID}, nil

	case dashboardID != 0:
		return View{Type: Dashboard, ID: dashboardID}, nil

	default:
		return View{}, errors.New("invalid view: requires question or dashboard ID")
	}
}

// createParams generates the appropriate params based on view type
func createParams(v View, dr DateRange, userName, customerName string) map[string]any {
	switch v.Type {
	case Dashboard:
		searchByUser := make([]string, 0)
		searchBySuggestedCustomer := make([]string, 0)

		if userName != "" {
			searchByUser = append(searchByUser, userName)
		}

		if customerName != "" {
			searchBySuggestedCustomer = append(searchBySuggestedCustomer, customerName)
		}
		return map[string]any{
			"date":                         fmt.Sprintf("%s~%s", dr.From, dr.To),
			"search_by_user":               searchByUser,
			"search_by_suggested_customer": searchBySuggestedCustomer,
		}

	case Question:
		return map[string]any{
			"start_date": dr.From,
			"end_date":   dr.To,
		}

	default:
		return nil
	}
}

// createClaims generates claims for the given view
func createClaims(v View, dr DateRange, userName, customerName string) jwt.MapClaims {
	return jwt.MapClaims{
		"resource": map[string]any{
			string(v.Type): v.ID,
		},
		"params": createParams(v, dr, userName, customerName),
		"exp":    time.Now().Add(tokenExpiry).Unix(),
	}
}

// generateToken creates a signed JWT token from claims
func generateToken(claims jwt.MapClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(env.Vars.MetabaseSecretKey))
}

// createEmbedURL generates the final embed URL
func createEmbedURL(v View, token string) string {
	return fmt.Sprintf(
		"%s/embed/%s/%s#bordered=true&titled=true",
		env.Vars.MetabaseSiteURL,
		v.Type,
		token,
	)
}

// extractUUIDFromURL extracts the UUID from a Metabase public dashboard URL
func extractUUIDFromURL(url string) string {
	// Example URL: https://metabase.example.com/public/dashboard/abcd1234-5678-90ab-cdef-1234567890ab
	re := regexp.MustCompile(`/public/(?:[^/]+)/([a-f0-9-]+)`)

	matches := re.FindStringSubmatch(url)
	if len(matches) > 1 {
		return matches[1]
	}

	return ""
}

func generateIFrameURL(
	ctx context.Context,
	dashboardID,
	questionID int,
	dateRange DateRange,
	userName,
	customerName string,
) string {

	view, err := newView(questionID, dashboardID)
	if err != nil {
		log.Error(ctx, "invalid view parameters", zap.Error(err))
		return ""
	}

	claims := createClaims(view, dateRange, userName, customerName)

	token, err := generateToken(claims)
	if err != nil {
		log.Error(ctx, "failed to sign metabase token", zap.Error(err))
		return ""
	}

	return createEmbedURL(view, token)
}
