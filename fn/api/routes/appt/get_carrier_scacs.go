package appt

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/integrations/scheduling/yardview"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/redis"
)

type (
	GetCarrierScacsQuery struct {
		Source        string `json:"source" validate:"required"`
		RequestSource string `json:"requestSource" validate:"required"`
	}

	GetCarrierScacsResponse struct {
		CarrierScacs []models.CyclopsCarrierScacData `json:"scacs"`
	}
)

// NOTE: Only YardView uses this now
func GetCarrierScacs(c *fiber.Ctx) error {
	var query GetCarrierScacsQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	userID := middleware.UserIDFromContext(c)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "GetUserByID failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	switch query.Source {
	case string(models.YardView):
		return handleYardViewCarrierScacs(c, user, &query)

	default:
		return c.Status(http.StatusBadRequest).SendString("unsupported scheduling integration")
	}
}

// handleYardViewCarrierScacs checks redis cache for carrier scacs first, if not found
// then fetches from yardview via Cyclops
func handleYardViewCarrierScacs(c *fiber.Ctx, user models.User, query *GetCarrierScacsQuery) error {
	ctx := log.With(
		c.UserContext(),
		zap.String("emailAddress", user.EmailAddress),
		zap.Uint("userID", user.ID),
		zap.Uint("serviceID", user.ServiceID),
		zap.String("schedulingIntegration", query.Source),
	)

	integration, err := integrationDB.GetSchedulerByServiceUserIDsAndName(
		ctx,
		user.ID,
		user.ServiceID,
		query.Source,
	)
	if err != nil {
		log.Errorf(ctx, "%s: %w", dbFetchSchedulingIntegrationError, err)
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Check Redis cache for carrier scacs
	cacheKey := fmt.Sprintf(
		"yardview-carrier_scacs-%d-%s-%s",
		user.ServiceID,
		integration.Username,
		query.RequestSource,
	)
	cachedScacs, found, redisErr := redis.GetKey[[]models.CyclopsCarrierScacData](ctx, cacheKey)
	if found && redisErr == nil {
		return c.Status(http.StatusOK).JSON(GetCarrierScacsResponse{CarrierScacs: cachedScacs})
	}

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		log.Errorf(ctx, "unable to connect to %s: %w", query.Source, err)
		return c.SendStatus(http.StatusInternalServerError)
	}

	yardViewClient, ok := client.(*yardview.YardView)
	if !ok {
		log.Errorf(ctx, "client for %s does not support GetCarrierScacs", query.Source)
		return c.SendStatus(http.StatusInternalServerError)
	}

	scacs, err := yardViewClient.GetCarrierScacs(ctx, query.RequestSource)
	if err != nil {
		log.Errorf(ctx, "%s.GetCarrierScacs failed: %w", query.Source, err)
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Store scacs in Redis with 3h TTL
	err = redis.SetKey(ctx, cacheKey, scacs, 3*time.Hour)
	if err != nil {
		log.WarnNoSentry(ctx, "error setting cached carrier scacs in redis", zap.Error(err))
	}

	return c.Status(http.StatusOK).JSON(GetCarrierScacsResponse{CarrierScacs: scacs})
}
