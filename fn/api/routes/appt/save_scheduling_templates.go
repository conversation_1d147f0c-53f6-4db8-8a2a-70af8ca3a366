package appt

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	apptDB "github.com/drumkitai/drumkit/common/rds/appt"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	SaveApptTemplateBody struct {
		ID                uint                     `json:"id"`
		TemplateType      models.EmailTemplateType `json:"templateType"`
		Subject           string                   `json:"subject"`
		Body              string                   `json:"body"`
		FreightTrackingID string                   `json:"freightTrackingID"`
	}

	SaveApptTemplateResponse struct {
		TemplateID uint `json:"templateID"`
	}
)

func SaveApptEmailRequest(c *fiber.Ctx) error {
	var body SaveApptTemplateBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	ctx, metaSpan := otel.StartSpan(ctx, "SaveApptEmailRequest", nil)
	defer func() { metaSpan.End(nil) }()

	user, err := userDB.GetByEmail(ctx, middleware.ClaimsFromContext(c).Email)
	if err != nil {
		return apiutil.HandleDBError(c, err, false, "user %s not found", middleware.ClaimsFromContext(c).Email)
	}

	ctx = log.With(ctx, zap.Uint("userID", user.ID), zap.Uint("serviceID", user.ServiceID))

	log.Info(ctx, "saving appointment email request", zap.Any("requestBody", body))

	load, err := loadDB.GetLoadByFreightIDAndService(ctx, user.ServiceID, body.FreightTrackingID)
	var loadID uint
	if err == nil {
		loadID = load.ID
	}

	appt := models.Appointment{
		Account:           user.EmailAddress,
		UserID:            user.ID,
		ServiceID:         user.ServiceID,
		FreightTrackingID: body.FreightTrackingID,
		LoadID:            loadID,
		EmailBody:         body.Body,
		EmailTemplateID:   body.ID,
		Source:            models.EmailRequest,
	}

	existingAppt, err := apptDB.GetByServiceIDAccountAndFreightTrackingIDForEmail(
		ctx,
		user.ServiceID,
		user.EmailAddress,
		body.FreightTrackingID,
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return apiutil.HandleDBError(c, err, false, "failed to query appointment")
	}

	if existingAppt.ID != 0 {
		existingAppt.EmailBody = appt.EmailBody
		existingAppt.EmailTemplateID = appt.EmailTemplateID

		if err := apptDB.Update(ctx, existingAppt); err != nil {
			return apiutil.HandleDBError(c, err, false, "failed to update appointment")
		}

		return c.Status(http.StatusOK).JSON(SaveApptTemplateResponse{TemplateID: body.ID})
	}

	if err := apptDB.Create(ctx, &appt); err != nil {
		return apiutil.HandleDBError(c, err, false, "failed to create appointment")
	}

	return c.Status(http.StatusOK).JSON(SaveApptTemplateResponse{TemplateID: body.ID})
}
