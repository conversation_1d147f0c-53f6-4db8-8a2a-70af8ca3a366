package mercurygate

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/http/cookiejar"
	"strings"
	"time"

	"go.uber.org/zap"
	"golang.org/x/net/publicsuffix"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
)

type SerializableCookie struct {
	Name    string
	Value   string
	Path    string
	Domain  string
	Expires time.Time
}

type SerializableMercuryGate struct {
	Config  *Config
	Cookies []SerializableCookie
}

type RateLimiter struct {
	RequestNumber int
	UpdatedAt     time.Time
}

func redisClientKey(serviceID uint, tmsID uint) string {
	return fmt.Sprintf("service-%d-tms-%d-mercurygate", serviceID, tmsID)
}

func retrieveRedisClient(ctx context.Context, serviceID uint, tmsID uint) *MercuryGate {
	sm, found, err := redis.GetKey[SerializableMercuryGate](ctx, redisClientKey(serviceID, tmsID))
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "failed to get MercuryGate session from Redis", zap.Error(err))
	}

	if found {
		cookies := make([]*http.Cookie, len(sm.Cookies))
		for i, sc := range sm.Cookies {
			cookies[i] = &http.Cookie{
				Name:    sc.Name,
				Value:   sc.Value,
				Path:    sc.Path,
				Domain:  sc.Domain,
				Expires: sc.Expires,
			}
		}

		client := otel.TracingHTTPClient()
		cookieJar, err := cookiejar.New(&cookiejar.Options{PublicSuffixList: publicsuffix.List})
		if err != nil {
			log.WarnNoSentry(ctx, "could not create MercuryGate cookie jar", zap.Error(err))
		} else {
			client.Jar = cookieJar
		}

		mercuryGateClient := &MercuryGate{
			config:     sm.Config,
			cookies:    cookies,
			httpClient: client,
		}

		log.Info(ctx, "re-using existing MercuryGate client")
		return mercuryGateClient
	}

	return nil
}

func (m *MercuryGate) cacheClient(ctx context.Context) {
	serializableCookies := make([]SerializableCookie, len(m.cookies))
	for i, cookie := range m.cookies {
		serializableCookies[i] = SerializableCookie{
			Name:    cookie.Name,
			Value:   cookie.Value,
			Path:    cookie.Path,
			Domain:  cookie.Domain,
			Expires: cookie.Expires,
		}
	}

	sm := SerializableMercuryGate{
		Config:  m.config,
		Cookies: serializableCookies,
	}

	redisKey := redisClientKey(m.tms.ServiceID, m.tms.ID)
	err := redis.SetKey(ctx, redisKey, sm, 3*time.Hour)
	if err != nil {
		log.Warn(ctx, "failed to set MercuryGate session in Redis", zap.Error(err))
	}
}

func (m *MercuryGate) sessionRefreshCheck(ctx context.Context, htmlBody string) {
	if strings.Contains(strings.ToLower(htmlBody), "your session has timed out due to inactivity") ||
		strings.Contains(strings.ToLower(htmlBody), "no session credentials found") {

		log.Info(ctx, "refreshing mercurygate session")

		err := m.Auth(ctx)
		if err != nil {
			log.Error(ctx, "mercurygate session refresh failed", zap.Error(err))
			return
		}

		m.cacheClient(ctx)
	}
}

func (m *MercuryGate) rateLimitCheck(ctx context.Context) error {
	config := helpers.RateLimitConfig{
		MaxRequests:    600,
		TimeWindow:     1 * time.Minute,
		RedisKeyPrefix: "mercurygate",
		Tenant:         m.config.Tenant,
	}

	return helpers.CheckRateLimit(ctx, config)
}
