package mercurygate

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/http/cookiejar"
	"time"

	"go.uber.org/zap"
	"golang.org/x/net/publicsuffix"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

type Config struct {
	Password string
	Tenant   string
	UserName string
}

type MercuryGate struct {
	tms        models.Integration
	httpClient httpClient
	cookies    []*http.Cookie
	config     *Config
}

// Define interface for testing purposes
type httpClient interface {
	Do(req *http.Request) (*http.Response, error)
}

func New(ctx context.Context, tms models.Integration) (*MercuryGate, error) {
	log.With(ctx, zap.Uint("axleTMSID", tms.ID), zap.String("tmsName", "mercurygate"))

	// Try retrieve an existing client
	cachedClient := retrieveRedisClient(ctx, tms.ServiceID, tms.ID)

	if cachedClient != nil {
		// We've successfully found a cached client for MercuryGate

		// For security reasons, do not rely on the serialized TMS
		cachedClient.tms = tms

		return cachedClient, nil
	}

	// Initialize a new MercuryGate client
	mercurygate, err := initialize(ctx, tms)
	if err != nil {
		return nil, err
	}

	// Authenticate the newly created client
	err = mercurygate.Auth(ctx)
	if err != nil {
		return nil, fmt.Errorf("authentication failed: %w", err)
	}

	mercurygate.cacheClient(ctx)

	return mercurygate, nil
}

func initialize(ctx context.Context, tms models.Integration) (*MercuryGate, error) {
	password, err := crypto.DecryptAESGCM(ctx, string(tms.EncryptedPassword), nil)
	if err != nil {
		return nil, fmt.Errorf("error decrypting password: %w", err)
	}

	if tms.Tenant == "" || password == "" {
		return nil, errors.New("missing MercuryGate password and/or app ID")
	}

	config := &Config{
		Password: password,
		Tenant:   tms.Tenant,
		UserName: tms.Username,
	}

	cookieJar, err := cookiejar.New(&cookiejar.Options{PublicSuffixList: publicsuffix.List})
	if err != nil {
		return nil, fmt.Errorf("could not create MercuryGate cookie jar: %w", err)
	}

	httpClient := otel.TracingHTTPClient()
	httpClient.Timeout = 20 * time.Second
	httpClient.Jar = cookieJar
	mercurygate := MercuryGate{
		tms:        tms,
		httpClient: httpClient,
		config:     config,
	}

	return &mercurygate, nil
}

func (m MercuryGate) GetTestLoads() map[string]bool {
	return map[string]bool{
		"10022096EVL": true, // Evans Trans Test Load
		"10022098EVL": true, // Evans Trans Test Load
	}
}

func (m MercuryGate) GetCustomers(ctx context.Context) (customers []models.TMSCustomer, _ error) {
	// return customers, helpers.NotImplemented(models.MercuryGate, "GetCustomers")
	return loadDB.GetListOfUniqueCustomers(ctx, m.tms.ID)
}

func (m MercuryGate) GetUsers(context.Context) (users []models.TMSUser, _ error) {
	return users, helpers.NotImplemented(models.MercuryGate, "GetUsers")
}

func (m MercuryGate) GetLocations(context.Context, ...models.TMSOption) (res []models.TMSLocation, _ error) {
	return res, helpers.NotImplemented(models.MercuryGate, "GetLocations")
}

func (m MercuryGate) GetCarriers(context.Context) ([]models.TMSCarrier, error) {
	return nil, helpers.NotImplemented(models.MercuryGate, "GetCarriers")
}
func (m MercuryGate) PostException(context.Context, *models.Load, models.Exception) error {
	return helpers.NotImplemented(models.MercuryGate, "PostException")
}

func (m MercuryGate) GetExceptionHistory(context.Context, uint, string) ([]models.Exception, error) {
	return []models.Exception{}, helpers.NotImplemented(models.MercuryGate, "GetExceptionHistory")
}

func (m MercuryGate) PostNote(context.Context, *models.Load, models.Note) ([]models.Note, error) {
	return nil, helpers.NotImplemented(models.MercuryGate, "PostNote")
}
