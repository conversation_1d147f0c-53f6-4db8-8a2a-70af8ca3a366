package turvo

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsCarrierDB "github.com/drumkitai/drumkit/common/rds/tms_carrier"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
	"github.com/drumkitai/drumkit/common/redis"
)

const (
	locationListPath        = "/v1/locations/list"
	appTurvoLocationListURL = "https://app.turvo.com/api/locations/list"

	// DEV URL
	// sandboxAppTurvoLocationListURL = "https://app.sandbox.turvo.com/api/locations/list"
)

// TODO after SDS: refresh/upsert locations properly

// GetLocations retrieves locations from Turvo. If changeHostName option is true use the app.turvo.com API endpoint.
func (t *Turvo) GetLocations(ctx context.Context, opts ...models.TMSOption) ([]models.TMSLocation, error) {
	options := &models.TMSOptions{}
	options.Apply(opts...)

	ctx, metaSpan := otel.StartSpan(ctx, "GetLocationsTurvo", otel.IntegrationAttrs(t.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	// If optional parameter is set to true, use the app.turvo.com API endpoint
	if options.ChangeHostName {
		return t.getLocationsAppTurvo(ctx)
	}

	// Standard location fetching using publicapi.turvo.com
	queryParams := make(url.Values)

	// First, check if we have a saved job state in redis
	updatedAt, cursor, err := redis.GetIntegrationState(ctx, t.tms.ID, redis.LocationJob)
	if err != nil {
		log.WarnNoSentry(ctx, "failed to get integration state", zap.Error(err))
	} else {
		if updatedAt != "" {
			queryParams.Set("updated[gte]", updatedAt)
		}
		if cursor != "" {
			queryParams.Set("start", cursor)
		}
	}

	// if we haven't set the updated[gte] query param by now, check the DB for the last update time
	if queryParams.Get("updated[gte]") == "" {
		latestUpdatedAt, err := integrationDB.GetColumn(ctx, t.tms.ID, integrationDB.LastLocationUpdatedAt)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				log.Info(
					ctx,
					"record not found, fetching all locations for integration",
					zap.Uint("integration_id", t.tms.ID),
				)
			} else {
				log.WarnNoSentry(
					ctx,
					"failed to get integration state, fetching all locations for integration",
					zap.Error(err),
				)
			}
		} else {
			queryParams.Set("updated[gte]", latestUpdatedAt.Format("2006-01-02T15:04:05Z"))
		}
	}

	var locations []models.TMSLocation

	for {
		var locationResp LocationListResponse
		if err = t.getWithAuth(
			ctx,
			locationListPath,
			queryParams,
			&locationResp,
			s3backup.TypeLocations,
		); err != nil {
			if err := redis.SetIntegrationState(
				ctx,
				t.tms.ID,
				redis.LocationJob,
				queryParams.Get("updated[gte]"),
				queryParams.Get("start"),
			); err != nil {
				log.Error(ctx, "failed to set integration state", zap.Error(err))
			}
			break
		}

		if strings.EqualFold(locationResp.Status, "error") {
			err = fmt.Errorf("turvo API error: %s", locationResp.Details.ErrorMessage)
			if err := redis.SetIntegrationState(
				ctx,
				t.tms.ID,
				redis.LocationJob,
				queryParams.Get("updated[gte]"),
				queryParams.Get("start"),
			); err != nil {
				log.Error(ctx, "failed to set integration state", zap.Error(err))
			}
			break
		}

		locationsToRefresh := &[]models.TMSLocation{}
		*locationsToRefresh = make([]models.TMSLocation, 0, len(locationResp.Details.Locations))
		for _, loc := range locationResp.Details.Locations {
			item := ToLocationModel(t.tms.ID, loc)
			if tz, err := t.lookupTimezone(ctx, item.ExternalTMSID); err != nil {
				if fallbackTz, fallbackErr := timezone.GetTimezoneByZipOrCity(
					ctx,
					item.Zipcode,
					item.City,
					item.State,
					item.Country,
				); fallbackErr == nil {
					item.Timezone = fallbackTz
				} else {
					log.WarnNoSentry(ctx, "failed to get timezone", zap.Error(fallbackErr))
				}
			} else {
				item.Timezone = tz
			}
			*locationsToRefresh = append(*locationsToRefresh, item)
		}

		// Upsert locations to db
		if err = tmsLocationDB.RefreshTMSLocations(ctx, locationsToRefresh); err != nil {
			if err := redis.SetIntegrationState(
				ctx,
				t.tms.ID,
				redis.LocationJob,
				queryParams.Get("updated[gte]"),
				queryParams.Get("start"),
			); err != nil {
				log.Error(ctx, "failed to set integration state", zap.Error(err))
			}
			break
		}

		// after successfully upserting, add the locations to the function's return slice
		locations = append(locations, *locationsToRefresh...)

		if !locationResp.Details.Pagination.MoreAvailable {
			// delete the redis key once we've successfully reached the end of the job
			err = redis.DeleteKey(ctx, fmt.Sprintf("integration-id-%d-%s", t.tms.ID, redis.LocationJob))
			if err != nil {
				log.Warn(ctx, "failed to delete redis key", zap.Error(err))
			}
			break
		}

		queryParams.Set("start", strconv.Itoa(locationResp.Details.Pagination.Start))
	}

	return locations, err
}

func ToLocationModel(tmsID uint, l LocationOverview) models.TMSLocation {

	var partialAddress PartialAddress
	if len(l.Address) > 0 {
		partialAddress = l.Address[0]
	} else if len(l.Addresses) > 0 {
		partialAddress = l.Addresses[0]
	}

	var phoneNumber string
	if len(l.Phones) > 0 {
		phoneNumber = l.Phones[0].Number
	}

	loc := models.TMSLocation{
		TMSIntegrationID: tmsID,
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: strconv.Itoa(l.ID),
			Name:          l.Name,
			AddressLine1:  partialAddress.Line1,
			AddressLine2:  partialAddress.Line2,
			City:          partialAddress.City,
			State:         partialAddress.State,
			Zipcode:       partialAddress.Zip,
			Phone:         phoneNumber,
		},
	}
	loc.FullAddress = models.ConcatAddress(loc.CompanyCoreInfo)
	loc.NameAddress = loc.Name + ", " + loc.FullAddress
	return loc

}

//-- For Carrier Locations (Carrier Quote) --//

// getLocationsAppTurvo fetches carrier locations from app.turvo.com/api
// This endpoint allows querying by tags so we can search locations tagged with "carrier"
func (t *Turvo) getLocationsAppTurvo(ctx context.Context) ([]models.TMSLocation, error) {
	queryParams := make(url.Values)
	queryParams.Set("types", `["all"]`)

	carriers, err := tmsCarrierDB.GetTMSCarriersByTMSID(ctx, rds.GenericGetQuery{TMSID: t.tms.ID})
	if err != nil {
		log.WarnNoSentry(ctx, "failed to fetch carriers from DB", zap.Error(err))
	}

	carrierMap := make(map[string]models.TMSCarrier, len(carriers))
	for _, carrier := range carriers {
		carrierMap[carrier.ExternalTMSID] = carrier
	}

	filter := map[string]any{
		"pageSize": 100,
		"start":    0,
		"criteria": []map[string]any{
			{
				"key":      "tags.tagName",
				"function": "in",
				"values":   []string{"carrier"},
			},
		},
		"sortBy":        "name",
		"sortDirection": "asc",
	}

	filterJSON, err := json.Marshal(filter)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal filter: %w", err)
	}
	queryParams.Set("filter", string(filterJSON))

	// Construct the full URL with query parameters
	fullURL, err := url.Parse(appTurvoLocationListURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse base URL: %w", err)
	}
	fullURL.RawQuery = queryParams.Encode()

	log.Info(ctx, "fetching carrier locations from app.turvo.com")
	var locations []models.TMSLocation
	var resp AppTurvoLocationListResponse

	if err := t.getWithAuth(
		ctx,
		fullURL.String(),
		make(url.Values),
		&resp,
		s3backup.TypeLocations,
	); err != nil {
		return nil, fmt.Errorf("failed to fetch locations from app.turvo.com: %w", err)
	}

	log.Info(ctx, "processing locations", zap.Int("total_locations", len(resp.Locations)))

	start := 0
	pageSize := 100

	for {
		filter["start"] = start
		filter["pageSize"] = pageSize

		filterJSON, err := json.Marshal(filter)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal filter: %w", err)
		}
		queryParams.Set("filter", string(filterJSON))

		fullURL.RawQuery = queryParams.Encode()

		var resp AppTurvoLocationListResponse
		if err := t.getWithAuth(
			ctx,
			fullURL.String(),
			make(url.Values),
			&resp,
			s3backup.TypeLocations,
		); err != nil {
			return nil, fmt.Errorf("failed to fetch locations from app.turvo.com: %w", err)
		}

		log.Info(ctx, "processing locations", zap.Int("page_start", start))

		for _, loc := range resp.Locations {
			tmsLoc, err := ToLocationModelAppTurvo(ctx, t, t.tms.ID, loc, carrierMap)
			if err != nil {
				log.WarnNoSentry(ctx, "failed to convert app.turvo location", zap.Error(err))
				continue
			}
			locations = append(locations, tmsLoc)
		}

		// Check if there's more data
		if !resp.Pagination.MoreAvailable {
			break
		}

		start += pageSize
	}

	log.Info(
		ctx,
		"processed locations and carriers",
		zap.Int("total_locations", len(locations)),
	)

	// Store the locations in the database
	if len(locations) > 0 {
		locationsPtr := &locations
		if err := tmsLocationDB.RefreshTMSLocations(ctx, locationsPtr); err != nil {
			return nil, fmt.Errorf("failed to refresh TMS locations: %w", err)
		}
		log.Info(ctx, "successfully stored locations in database", zap.Int("count", len(locations)))
	}

	return locations, nil
}

// ToLocationModelAppTurvo converts an AppTurvoLocationDetail to a TMSLocation model
func ToLocationModelAppTurvo(
	ctx context.Context,
	t *Turvo,
	tmsID uint,
	l AppTurvoLocationDetail,
	carrierMap map[string]models.TMSCarrier,
) (models.TMSLocation, error) {

	var phoneNumber string
	var latitude, longitude float64
	var addressLine1, addressLine2, city, state, zipcode string

	// Extract phone number
	for _, phone := range l.Basic.Phones {
		if phone.Primary {
			phoneNumber = phone.Number
			break
		}
	}

	// If no primary phone found, use the first one
	if phoneNumber == "" && len(l.Basic.Phones) > 0 {
		phoneNumber = l.Basic.Phones[0].Number
	}

	var primaryEmail string
	var allEmails []string

	for _, e := range l.Basic.Emails {
		if e.Active && !e.Deleted {
			allEmails = append(allEmails, e.Email)
			if e.Primary {
				primaryEmail = e.Email
			}
		}
	}

	// If no primary email was found but we have active emails, use the first one as primary
	if primaryEmail == "" && len(allEmails) > 0 {
		primaryEmail = allEmails[0]
	}

	// Extract address and GPS coordinates
	for _, addr := range l.Basic.Addresses {
		if addr.Primary {
			addressLine1 = addr.Line1
			addressLine2 = addr.Line2
			if addr.City.Name != "" {
				city = addr.City.Name
			}
			if addr.State.Name != "" {
				state = addr.State.Name
			}
			zipcode = addr.Zip

			// GPS coordinates are provided as [longitude, latitude]
			if len(addr.GPS.Coordinates) >= 2 {
				longitude = addr.GPS.Coordinates[0]
				latitude = addr.GPS.Coordinates[1]
			}
			break
		}
	}

	// If no primary address found, use the first one
	if addressLine1 == "" && len(l.Basic.Addresses) > 0 {
		addressLine1 = l.Basic.Addresses[0].Line1
		addressLine2 = l.Basic.Addresses[0].Line2
		if l.Basic.Addresses[0].City.Name != "" {
			city = l.Basic.Addresses[0].City.Name
		}
		if l.Basic.Addresses[0].State.Name != "" {
			state = l.Basic.Addresses[0].State.Name
		}
		zipcode = l.Basic.Addresses[0].Zip

		// GPS coordinates are provided as [longitude, latitude]
		if len(l.Basic.Addresses[0].GPS.Coordinates) >= 2 {
			longitude = l.Basic.Addresses[0].GPS.Coordinates[0]
			latitude = l.Basic.Addresses[0].GPS.Coordinates[1]
		}
	}

	loc := models.TMSLocation{
		TMSIntegrationID: tmsID,
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: strconv.Itoa(l.ID),
			Name:          l.Basic.Name,
			AddressLine1:  addressLine1,
			AddressLine2:  addressLine2,
			City:          city,
			State:         state,
			Zipcode:       zipcode,
			Phone:         phoneNumber,
			Email:         primaryEmail,
		},
		Emails:    allEmails,
		Latitude:  latitude,
		Longitude: longitude,
		Point: models.Point{
			Latitude:  float32(latitude),
			Longitude: float32(longitude),
		},
	}

	if tz, err := t.lookupTimezone(ctx, strconv.Itoa(l.ID)); err != nil {
		if fallbackTz, fallbackErr := timezone.GetTimezoneByZipOrCity(
			ctx,
			loc.Zipcode,
			loc.City, loc.State,
			loc.Country,
		); fallbackErr == nil {
			loc.Timezone = fallbackTz
		} else {
			log.WarnNoSentry(ctx, "failed to get timezone", zap.Error(fallbackErr))
		}
	} else {
		loc.Timezone = tz
	}

	// Calculate the full address
	loc.FullAddress = models.ConcatAddress(loc.CompanyCoreInfo)
	loc.NameAddress = loc.Name + ", " + loc.FullAddress

	// Carrier association
	if l.Basic.Account.ID > 0 {
		carrierExternalID := strconv.Itoa(l.Basic.Account.ID)

		// Check if carrier exists in map
		existingCarrier, exists := carrierMap[carrierExternalID]

		switch {
		case exists:
			// Carrier exists, use its ID
			loc.TMSCarrierID = existingCarrier.ID
		default:
			// Carrier doesn't exist, fetch from API and create
			var carrierResp CarrierResponse
			err := t.getWithAuth(
				ctx,
				fmt.Sprintf("/v1/carriers/%d", l.Basic.Account.ID),
				make(url.Values),
				&carrierResp,
				s3backup.TypeCarriers,
			)
			if err != nil {
				log.WarnNoSentry(
					ctx,
					"failed to fetch carrier details, skipping carrier association",
					zap.Error(err),
					zap.Int("carrier_id", l.Basic.Account.ID),
				)
			} else {
				// Add primary email and collect all active emails for the carrier
				var allCarrierEmails []string
				var primaryCarrierEmail string
				for _, email := range carrierResp.Details.Email {
					if !email.Deleted {
						allCarrierEmails = append(allCarrierEmails, email.Email)
						if email.IsPrimary {
							primaryCarrierEmail = email.Email
						}
					}
				}
				// If no primary email was found but we have active emails, use the first one
				if primaryCarrierEmail == "" && len(allCarrierEmails) > 0 {
					primaryCarrierEmail = allCarrierEmails[0]
				}

				newCarrier := models.TMSCarrier{
					TMSIntegrationID: tmsID,
					CompanyCoreInfo: models.CompanyCoreInfo{
						ExternalTMSID: carrierExternalID,
						Name:          carrierResp.Details.Name,
						Email:         primaryCarrierEmail,
					},
					Emails:    allCarrierEmails,
					ServiceID: t.tms.ServiceID,

					// Handle DotNumber typed as any
					DOTNumber: func() string {
						switch v := carrierResp.Details.DotNumber.(type) {
						case string:
							return v
						case float64:
							return strconv.FormatFloat(v, 'f', -1, 64)
						case int:
							return strconv.Itoa(v)
						case nil:
							return ""
						default:
							log.Warn(
								ctx,
								"Unexpected type for carrierResp.Details.DotNumber in locations.go",
								zap.Any("dotNumberValue", v),
								zap.Int("carrierID", carrierResp.Details.ID),
							)
							return fmt.Sprintf("%v", v)
						}
					}(),
				}

				// Add primary address if available
				for _, addr := range carrierResp.Details.Address {
					if addr.IsPrimary {
						newCarrier.AddressLine1 = addr.Line1
						newCarrier.AddressLine2 = addr.Line2
						newCarrier.City = addr.City
						newCarrier.State = addr.State
						newCarrier.Zipcode = addr.Zip
						break
					}
				}

				// Add primary phone if available
				for _, phone := range carrierResp.Details.Phone {
					if phone.IsPrimary {
						newCarrier.Phone = phone.Number
						break
					}
				}

				if err := tmsCarrierDB.CreateCarrier(ctx, &newCarrier); err != nil {
					log.WarnNoSentry(
						ctx,
						"failed to create carrier",
						zap.Error(err),
						zap.String("carrier_external_id", carrierExternalID),
					)
				} else {
					loc.TMSCarrierID = newCarrier.ID
				}
			}
		}
	}

	return loc, nil
}
