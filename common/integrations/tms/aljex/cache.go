package aljex

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/http/cookiejar"
	"strings"
	"time"

	"go.uber.org/zap"
	"golang.org/x/net/publicsuffix"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
)

type SerializableCookie struct {
	Name    string    `json:"name"`
	Value   string    `json:"value"`
	Path    string    `json:"path"`
	Domain  string    `json:"domain"`
	Expires time.Time `json:"expires"`
}

type SerializableAljex struct {
	Config  *Config              `json:"config"`
	Creds   *Credentials         `json:"creds"`
	Cookies []SerializableCookie `json:"cookies"`
}

func redisClientKey(serviceID uint, tmsID uint) string {
	return fmt.Sprintf("service-%d-tms-%d-aljex", serviceID, tmsID)
}

func retrieveRedisClient(ctx context.Context, serviceID uint, tmsID uint) *Aljex {
	sa, found, err := redis.GetKey[SerializableAljex](ctx, redisClientKey(serviceID, tmsID))
	if err != nil {
		if !errors.Is(err, redis.NilEntry) {
			log.Warn(ctx, "failed to get Aljex session from Redis", zap.Error(err))
		}
		return nil
	}

	if !found {
		return nil
	}

	cookies := make([]*http.Cookie, len(sa.Cookies))
	for i, sc := range sa.Cookies {
		cookies[i] = &http.Cookie{
			Name:    sc.Name,
			Value:   sc.Value,
			Path:    sc.Path,
			Domain:  sc.Domain,
			Expires: sc.Expires,
		}
	}

	client := otel.TracingHTTPClient(120 * time.Second)
	cookieJar, err := cookiejar.New(&cookiejar.Options{PublicSuffixList: publicsuffix.List})
	if err != nil {
		log.Debug(ctx, "could not create Aljex cookie jar", zap.Error(err))
	} else {
		client.Jar = cookieJar
	}

	log.Info(ctx, "re-using existing Aljex client")
	return &Aljex{
		httpClient: client,
		cookies:    cookies,
		config:     sa.Config,
		creds:      sa.Creds,
	}
}

func (a *Aljex) cacheClient(ctx context.Context) {
	sa := SerializableAljex{
		Config:  a.config,
		Creds:   a.creds,
		Cookies: make([]SerializableCookie, len(a.cookies)),
	}

	for i, cookie := range a.cookies {
		sa.Cookies[i] = SerializableCookie{
			Name:    cookie.Name,
			Value:   cookie.Value,
			Path:    cookie.Path,
			Domain:  cookie.Domain,
			Expires: cookie.Expires,
		}
	}

	redisKey := redisClientKey(a.tms.ServiceID, a.tms.ID)
	if err := redis.SetKey(ctx, redisKey, sa, 3*time.Hour); err != nil {
		log.Warn(ctx, "failed to set Aljex session in Redis", zap.Error(err))
	}
}

func (a *Aljex) sessionRefreshCheck(ctx context.Context, htmlBody string) {
	if strings.Contains(strings.ToLower(htmlBody), "your session has timed out due to inactivity") ||
		strings.Contains(strings.ToLower(htmlBody), "no session credentials found") {

		log.Info(ctx, "refreshing aljex session")

		err := a.Auth(ctx)
		if err != nil {
			log.Error(ctx, "aljex session refresh failed", zap.Error(err))
			return
		}

		a.cacheClient(ctx)
	}
}

func (a *Aljex) rateLimitCheck(ctx context.Context) error {
	config := helpers.RateLimitConfig{
		MaxRequests:    600,
		TimeWindow:     1 * time.Minute,
		RedisKeyPrefix: "aljex",
		Tenant:         a.config.Tenant,
	}

	return helpers.CheckRateLimit(ctx, config)
}
