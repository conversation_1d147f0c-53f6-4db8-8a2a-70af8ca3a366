package aljex

import (
	"context"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	commonredis "github.com/drumkitai/drumkit/common/redis"
)

func TestNewAljexFromSerializable(t *testing.T) {
	t.Run("creates client with cookies", func(t *testing.T) {
		sa := SerializableAljex{
			Config: &Config{
				Tenant: "test-tenant",
			},
			Creds: &Credentials{
				Name: "test-user",
			},
			Cookies: []SerializableCookie{
				{
					Name:    "session",
					Value:   "12345",
					Domain:  "example.com",
					Path:    "/",
					Expires: time.Now().Add(1 * time.Hour),
				},
			},
		}

		ctx := context.Background()
		aljexClient, err := NewAljexFromSerializable(ctx, sa)

		require.NoError(t, err)
		require.NotNil(t, aljexClient)

		assert.Equal(t, sa.Config, aljexClient.config)
		assert.Equal(t, sa.Creds, aljexClient.creds)
		require.Len(t, aljexClient.cookies, 1)
		assert.Equal(t, "session", aljexClient.cookies[0].Name)
		assert.Equal(t, "12345", aljexClient.cookies[0].Value)
		assert.NotNil(t, aljexClient.httpClient)
	})

	t.Run("creates client with no cookies", func(t *testing.T) {
		sa := SerializableAljex{
			Config: &Config{
				Tenant: "test-tenant-no-cookie",
			},
			Creds: &Credentials{
				Name: "test-user-no-cookie",
			},
			Cookies: []SerializableCookie{},
		}

		ctx := context.Background()
		aljexClient, err := NewAljexFromSerializable(ctx, sa)

		require.NoError(t, err)
		require.NotNil(t, aljexClient)

		assert.Equal(t, sa.Config, aljexClient.config)
		assert.Equal(t, sa.Creds, aljexClient.creds)
		assert.Len(t, aljexClient.cookies, 0)
		assert.NotNil(t, aljexClient.httpClient)
	})
}

func TestRetrieveRedisClient(t *testing.T) {
	ctx := context.Background()
	serviceID := uint(1)
	tmsID := uint(2)
	redisKey := "service-1-tms-2-aljex"

	db, mock := redismock.NewClientMock()

	originalRDB := commonredis.RDB
	commonredis.RDB = db
	defer func() { commonredis.RDB = originalRDB }()

	t.Run("client found in redis", func(t *testing.T) {
		sa := SerializableAljex{
			Config:  &Config{Tenant: "found"},
			Creds:   &Credentials{Name: "found-user"},
			Cookies: []SerializableCookie{},
		}
		saBytes, err := json.Marshal(sa)
		require.NoError(t, err)

		mock.ExpectGet(redisKey).SetVal(string(saBytes))

		client, err := retrieveRedisClient(ctx, serviceID, tmsID)

		require.NoError(t, err)
		require.NotNil(t, client)
		assert.Equal(t, "found", client.config.Tenant)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("client not found in redis", func(t *testing.T) {
		mock.ExpectGet(redisKey).RedisNil()

		client, err := retrieveRedisClient(ctx, serviceID, tmsID)

		require.NoError(t, err)
		assert.Nil(t, client)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("redis returns an error", func(t *testing.T) {
		mock.ExpectGet(redisKey).SetErr(errors.New("redis error"))

		client, err := retrieveRedisClient(ctx, serviceID, tmsID)

		require.NoError(t, err)
		assert.Nil(t, client)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("unmarshal error", func(t *testing.T) {
		mock.ExpectGet(redisKey).SetVal("this is not json")

		client, err := retrieveRedisClient(ctx, serviceID, tmsID)

		require.NoError(t, err)
		assert.Nil(t, client)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
