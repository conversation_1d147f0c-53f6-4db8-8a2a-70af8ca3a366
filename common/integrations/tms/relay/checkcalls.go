package relay

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

const initCID = 5

func (r *Relay) GetCheckCallsHistory(
	ctx context.Context,
	loadID uint,
	freightTrackingID string,
) (checkcalls []models.CheckCall, err error) {

	spanAttrs := append(
		otel.IntegrationAttrs(r.tms),
		otel.SafeIntAttribute("load_id", loadID),
		attribute.String("freight_tracking_id", freightTrackingID),
	)

	ctx, metaSpan := otel.StartSpan(ctx, "GetCheckCallsHistoryRelay", spanAttrs)
	defer func() { metaSpan.End(err) }()

	respBody, err := r.getHTML(ctx, "tracking/tracking_load_detail/"+url.PathEscape(freightTrackingID),
		nil, s3backup.TypeCheckCalls)
	if err != nil {
		return nil, err
	}

	return r.parseCheckCallHTML(ctx, respBody, loadID, freightTrackingID)
}

// PostCheckCall posts a check call to Relay, with retry logic to handle Relay updating the CID parameter.
// If CID is incorrect, then Relay WS returns phx_error{}.
func (r *Relay) PostCheckCall(
	ctx context.Context,
	load *models.Load,
	cc models.CheckCall,
) (err error) {
	spanAttrs := append(otel.IntegrationAttrs(r.tms),
		attribute.String("externalTMSID", load.ExternalTMSID))

	ctx, metaSpan := otel.StartSpan(ctx, "PostCheckCallRelay", spanAttrs)
	defer func() { metaSpan.End(err) }()

	badRequestErr := errtypes.HTTPResponseError{
		IntegrationName: r.tms.Name,
		IntegrationType: r.tms.Type,
		AxleTSPID:       r.tms.ID,
		ServiceID:       r.tms.ID,
		HTTPMethod:      "POST",
		URL:             "",
		StatusCode:      http.StatusBadRequest,
		ResponseHeaders: map[string][]string{},
		ResponseBody:    []byte("cannot POST check call without a carrier booked to the load"),
	}

	// Validate carrier exists
	if load.Carrier.Name == "" {
		return badRequestErr
	}

	// Determine form events based on check call status
	var modalFormEvent, submitFormEvent string
	stopIDsRequired := true

	switch strings.ToLower(cc.Status) {
	case "dispatch driver":
		stopIDsRequired = false
		modalFormEvent = "open_dispatch_driver_action_clicked"
		submitFormEvent = "dispatch_driver_clicked"
	case "in transit update":
		modalFormEvent = "open_capture_in_transit_udpate_action_clicked"
		submitFormEvent = "capture_in_transit_update_clicked"
	case "mark arrived at pickup", "mark arrived at delivery":
		modalFormEvent = "open_mark_arrived_at_stop_action_clicked"
		submitFormEvent = "mark_arrived_at_stop_clicked"
	case "mark loaded":
		modalFormEvent = "open_mark_loaded_action_clicked"
		submitFormEvent = "mark_loaded_clicked"
	case "mark delivered":
		modalFormEvent = "open_mark_delivered_action_clicked"
		submitFormEvent = "mark_delivered_clicked"
	case "add tracking note":
		stopIDsRequired = false
		modalFormEvent = "open_capture_tracking_note_action_clicked"
		submitFormEvent = "capture_tracking_note_clicked"
	}

	// Retry logic with incrementing CIDs
	startingCID := r.getWorkingCID(ctx)
	var lastErr error

	for attempt := 0; attempt < 3; attempt++ {
		currentCID := startingCID + attempt
		log.Info(
			ctx,
			"attempting check call submission",
			zap.Int("attempt", attempt+1),
			zap.Int("cid", currentCID),
		)

		// Initialize tracking board action
		params, err := r.initTrackingBoardAction(ctx, load, modalFormEvent, stopIDsRequired)
		if err != nil {
			lastErr = fmt.Errorf("error initializing tracking board action on attempt %d: %w", attempt+1, err)
			log.WarnNoSentry(ctx, lastErr.Error())
			continue
		}

		// Create check call payload
		ccQuery, err := r.createCheckCallPayload(ctx, cc, params, load)
		if err != nil {
			badRequestErr.ResponseBody = []byte(err.Error())
			return badRequestErr
		}

		// Create submit message
		submitMsg := wsPayload{
			OperationName: "Tracking Board - Submit check call",
			Message: []any{
				"4",
				"10",
				params.PhxID,
				"event",
				map[string]any{
					"type":  "form",
					"event": submitFormEvent,
					"value": ccQuery.Encode(),
					"cid":   currentCID,
				},
			},
			NumExpectedResponses: 2,
		}

		// Send websocket message
		_, err = r.sendWebSocketMessages(
			ctx,
			true,
			"tracking_board/live/websocket",
			params.QueryParams,
			true,
			submitMsg,
		)
		if err == nil {
			r.updateWorkingCID(ctx, currentCID)
			return nil
		}

		lastErr = fmt.Errorf("error sending submit msg on attempt %d: %w", attempt+1, err)
		log.WarnNoSentry(
			ctx,
			"check call submission failed",
			zap.Int("attempt", attempt+1),
			zap.Int("cid", currentCID),
			zap.Error(err),
		)
	}

	return fmt.Errorf("check call submission failed after all retry attempts: %w", lastErr)
}

// getWorkingCID retrieves the current working CID from Redis or returns the initial CID
func (r *Relay) getWorkingCID(ctx context.Context) int {
	cid := initCID
	if cidStr, _, err := redis.GetKey[string](ctx, "relay_checkcall_working_cid"); err == nil {
		if parsedCID, err := strconv.Atoi(cidStr); err == nil {
			log.Info(ctx, "using relay_checkcall_working_cid from redis", zap.Int("cid", parsedCID))
			return parsedCID
		}
		log.WarnNoSentry(ctx, "error converting redis CID to int, falling back to initCID", zap.Error(err))
	}
	return cid
}

// updateWorkingCID updates the working CID in Redis
func (r *Relay) updateWorkingCID(ctx context.Context, cid int) {
	log.Info(ctx, "updating redis CID", zap.Int("cid", cid))
	if err := redis.SetKey(ctx, "relay_checkcall_working_cid", strconv.Itoa(cid), 0); err != nil {
		log.WarnNoSentry(ctx, "error setting relay_checkcall_working_cid", zap.Error(err), zap.Int("cid", cid))
	}
}

// createCheckCallPayload creates the payload for a check call based on its type and parameters
func (r *Relay) createCheckCallPayload(
	ctx context.Context,
	cc models.CheckCall,
	params trackingBoardParams,
	load *models.Load,
) (url.Values, error) {
	// Map codes
	reasonCode, err := mapReasonToCode(cc.Reason)
	if err != nil {
		return nil, fmt.Errorf("invalid late reason: %w", err)
	}

	sourceCode, err := mapSourceToCode(cc.Source)
	if err != nil {
		return nil, fmt.Errorf("invalid source: %w", err)
	}

	// Format dates
	formattedDate := cc.DateTimeWithoutTimezone.Time.In(time.UTC).Format(time.DateOnly)
	formattedTime := cc.DateTimeWithoutTimezone.Time.In(time.UTC).Format("15:04")

	var formattedOutDate, formattedOutTime string
	if cc.EndDateTimeWithoutTimezone.Valid {
		formattedOutDate = cc.EndDateTimeWithoutTimezone.Time.In(time.UTC).Format(time.DateOnly)
		formattedOutTime = cc.EndDateTimeWithoutTimezone.Time.In(time.UTC).Format("15:04")
	}

	var formattedETADate, formattedETATime string
	if cc.NextStopETAWithoutTimezone.Valid {
		formattedETADate = cc.NextStopETAWithoutTimezone.Time.In(time.UTC).Format(time.DateOnly)
		formattedETATime = cc.NextStopETAWithoutTimezone.Time.In(time.UTC).Format("15:04")
	}

	ccQuery := url.Values{}
	switch s := strings.ToLower(cc.Status); s {
	case "dispatch driver":
		var location string
		if cc.City != "" || cc.State != "" {
			location = fmt.Sprintf("%s, %s", cc.City, cc.State)
		}
		ccQuery = url.Values{
			"_csrf_token":                                          []string{params.CSRFToken},
			"dispatch_driver[truck_load_thing_id]":                 []string{params.LoadThingID},
			"dispatch_driver[source]":                              []string{sourceCode},
			"dispatch_driver[truck_location]":                      []string{location},
			"dispatch_driver[truck_location_time][_persistent_id]": []string{"0"},
			"dispatch_driver[truck_location_time][date]":           []string{formattedDate},
			"dispatch_driver[truck_location_time][time]":           []string{formattedTime},
			"dispatch_driver[eta][_persistent_id]":                 []string{"0"},
			"dispatch_driver[eta][date]":                           []string{formattedETADate},
			"dispatch_driver[eta][time]":                           []string{formattedETATime},
			"dispatch_driver[notes]":                               []string{cc.Notes},
			"dispatch_driver[truck_number]":                        []string{load.Carrier.ExternalTMSTruckID},
			"dispatch_driver[trailer_number]":                      []string{load.Carrier.ExternalTMSTrailerID},
			"dispatch_driver[driver_name]":                         []string{load.Carrier.FirstDriverName},
			"dispatch_driver[driver_phone_number]":                 []string{load.Carrier.FirstDriverPhone},
		}

		if strings.TrimSpace(load.Carrier.SecondDriverName) != "" ||
			strings.TrimSpace(load.Carrier.SecondDriverPhone) != "" {
			ccQuery.Set("dispatch_driver[additional_drivers][0][_persistent_id]", "0")
			ccQuery.Set("dispatch_driver[additional_drivers][0][name]", load.Carrier.SecondDriverName)
			ccQuery.Set("dispatch_driver[additional_drivers][0][phone]", load.Carrier.SecondDriverPhone)
		}

	case "in transit update":
		tz, err := timezone.GetTimezone(ctx, cc.City, cc.State, "")
		if err != nil {
			log.WarnNoSentry(ctx, "error getting check call timezone", zap.Error(err))
		}

		var etaStopID string
		if strings.ToLower(cc.NextStopID) == "pickup" {
			etaStopID = params.OrderedStopIDs[0]
		} else {
			etaStopID = params.OrderedStopIDs[len(params.OrderedStopIDs)-1]
		}

		ccQuery = url.Values{
			"_csrf_token": {params.CSRFToken},
			"capture_in_transit_update[truck_load_thing_id]":                     {params.LoadThingID},
			"capture_in_transit_update[source]":                                  {sourceCode},
			"capture_in_transit_update[eta_stop_id]":                             {etaStopID},
			"capture_in_transit_update[eta][_persistent_id]":                     {"0"},
			"capture_in_transit_update[eta][date]":                               {formattedETADate},
			"capture_in_transit_update[eta][time]":                               {formattedETATime},
			"capture_in_transit_update[selected_location_locality]":              {},
			"capture_in_transit_update[selected_location_administrative_region]": {cc.State},
			"capture_in_transit_update[selected_location_timezone]":              {tz},
			"capture_in_transit_update[truck_location]": {
				fmt.Sprintf("%s, %s", cc.City, cc.State),
			},
			"capture_in_transit_update[truck_location_date_time][_persistent_id]": {"0"},
			"capture_in_transit_update[truck_location_date_time][date]":           {formattedDate},
			"capture_in_transit_update[truck_location_date_time][time]":           {formattedTime},
			"capture_in_transit_update[reason]":                                   {reasonCode},
			"capture_in_transit_update[notes]":                                    {cc.Notes},
		}

	case "mark arrived at pickup", "mark arrived at delivery":
		var stopID string
		if strings.Contains(s, "at pickup") {
			stopID = params.OrderedStopIDs[0]
		} else {
			stopID = params.OrderedStopIDs[len(params.OrderedStopIDs)-1]
		}

		ccQuery = url.Values{
			"_csrf_token": {params.CSRFToken},
			"mark_arrived_at_stop[truck_load_thing_id]":          {params.LoadThingID},
			"mark_arrived_at_stop[source]":                       {sourceCode},
			"mark_arrived_at_stop[stop_id]":                      {stopID},
			"mark_arrived_at_stop[in_date_time][_persistent_id]": {"0"},
			"mark_arrived_at_stop[in_date_time][date]":           {formattedDate},
			"mark_arrived_at_stop[in_date_time][time]":           {formattedTime},
			"mark_arrived_at_stop[reason]":                       {reasonCode},
			"mark_arrived_at_stop[notes]":                        {cc.Notes},
		}

		if cc.IsOnTime != nil {
			ccQuery.Add("mark_arrived_at_stop[is_on_time]", fmt.Sprint(*cc.IsOnTime))
		}

	case "mark delivered":
		ccQuery = url.Values{
			"_csrf_token":                         {params.CSRFToken},
			"mark_delivered[truck_load_thing_id]": {params.LoadThingID},
			"mark_delivered[source]":              {sourceCode},
			"mark_delivered[stop_id]": {
				params.OrderedStopIDs[len(params.OrderedStopIDs)-1],
			},
			"mark_delivered[in_delivery_date_time][_persistent_id]":  {"0"},
			"mark_delivered[in_delivery_date_time][date]":            {formattedDate},
			"mark_delivered[in_delivery_date_time][time]":            {formattedTime},
			"mark_delivered[out_delivery_date_time][_persistent_id]": {"0"},
			"mark_delivered[out_delivery_date_time][date]":           {formattedOutDate},
			"mark_delivered[out_delivery_date_time][time]":           {formattedOutTime},
			"mark_delivered[reason]":                                 {reasonCode},
			"mark_delivered[notes]":                                  {cc.Notes},
		}

		if cc.IsOnTime != nil {
			ccQuery.Add("mark_delivered[is_on_time]", fmt.Sprint(*cc.IsOnTime))
		}

	case "mark loaded":
		ccQuery = url.Values{
			"_csrf_token":                                {params.CSRFToken},
			"mark_loaded[truck_load_thing_id]":           {params.LoadThingID},
			"mark_loaded[source]":                        {sourceCode},
			"mark_loaded[stop_id]":                       {params.OrderedStopIDs[0]},
			"mark_loaded[in_date_time][_persistent_id]":  {"0"},
			"mark_loaded[in_date_time][date]":            {formattedDate},
			"mark_loaded[in_date_time][time]":            {formattedTime},
			"mark_loaded[out_date_time][_persistent_id]": {"0"},
			"mark_loaded[out_date_time][date]":           {formattedOutDate},
			"mark_loaded[out_date_time][time]":           {formattedOutTime},
			"mark_loaded[reason]":                        {reasonCode},
			"mark_loaded[notes]":                         {cc.Notes},
		}

		// Loaded requires is_on_time, even though "delivered" does not
		if cc.IsOnTime == nil {
			ccQuery.Add("mark_loaded[is_on_time]", "true")
		} else {
			ccQuery.Add("mark_loaded[is_on_time]", fmt.Sprint(*cc.IsOnTime))
		}

	case "add tracking note":
		ccQuery = url.Values{
			"_csrf_token": {params.CSRFToken},
			"capture_tracking_note[truck_load_thing_id]": {params.LoadThingID},
			"capture_tracking_note[source]":              {sourceCode},
			"capture_tracking_note[notes]":               {cc.Notes},
		}
		if cc.IsOnTime != nil {
			ccQuery.Add("capture_tracking_note[is_on_time]", fmt.Sprint(*cc.IsOnTime))
		}
		if cc.IsException != nil {
			ccQuery.Add("capture_tracking_note[issue?]", fmt.Sprint(*cc.IsException))
		}
	}

	return ccQuery, nil
}

//nolint:unused
func isValidLateReason(reason string) bool {
	validOptions := map[string]struct{}{
		"":                                       {},
		"accident":                               {},
		"consignee related":                      {},
		"driver related":                         {},
		"mechanical breakdown":                   {},
		"other carrier related":                  {},
		"previous stop":                          {},
		"shipper related":                        {},
		"holiday":                                {},
		"weather":                                {},
		"return to shipper":                      {},
		"driver not available":                   {},
		"processing delay":                       {},
		"trailer not available":                  {},
		"insufficient time to complete delivery": {},
	}

	_, exists := validOptions[(strings.ToLower(reason))]
	return exists
}

// parseCheckCallHTML parses HTML content to extract check call information from Relay TMS.
// It supports both legacy and new HTML formats for backward compatibility.
func (r *Relay) parseCheckCallHTML(
	ctx context.Context,
	respBody []byte,
	loadID uint,
	freightID string,
) ([]models.CheckCall, error) {
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(respBody))
	if err != nil {
		return nil, fmt.Errorf("failed to parse HTML: %w", err)
	}

	parser := &checkCallParser{
		loadID:    loadID,
		freightID: freightID,
	}

	// Find tracking update elements using both new and legacy selectors
	updateDivs := doc.Find("div.tracking-update.kt-tracking-event")
	foundWithNewSelector := updateDivs.Length() > 0

	if updateDivs.Length() == 0 {
		updateDivs = doc.Find("div.col-lg-3.stop-detail .tracking-update")
		if updateDivs.Length() > 0 {
			log.Info(
				ctx,
				"found check calls using legacy selector - HTML structure may have changed",
				zap.String("freight_tracking_id", freightID),
				zap.Int("check_calls_found", updateDivs.Length()),
				zap.String("tenant", r.tms.Tenant),
			)
		}
	} else {
		log.Debug(
			ctx,
			"found check calls using new selector",
			zap.String("freight_tracking_id", freightID),
			zap.Int("check_calls_found", updateDivs.Length()),
			zap.String("tenant", r.tms.Tenant),
		)
	}

	if updateDivs.Length() == 0 {
		// Log the HTML structure for debugging if no elements found
		bodyText := string(respBody)
		if len(bodyText) > 1000 {
			bodyText = bodyText[:1000]
		}

		log.WarnNoSentry(
			ctx,
			"no tracking updates found in HTML - expected for loads without tracking history",
			zap.String("freight_tracking_id", freightID),
			zap.Bool("found_with_new_selector", foundWithNewSelector),
		)

		log.Debug(ctx, "HTML preview for debugging selector issues", zap.String("html_preview", bodyText))
		return nil, nil
	}

	var checkCalls []models.CheckCall
	var emptyStatusCount, emptySourceCount int

	updateDivs.Each(func(_ int, s *goquery.Selection) {
		checkCall := parser.parseCheckCallFromElement(ctx, s)

		// Track critical missing fields for observability
		if checkCall.Status == "" {
			emptyStatusCount++
		}
		if checkCall.Source == "" {
			emptySourceCount++
		}

		checkCalls = append(checkCalls, checkCall)
	})

	// NOTE: Staging and Prod relay have different HTML, so adding warning in case changes are pushed to prod
	// and we need to update this function
	totalCheckCalls := len(checkCalls)
	if totalCheckCalls > 0 {
		if emptyStatusCount > 0 {
			log.WarnNoSentry(
				ctx,
				"check calls with empty status detected - HTML structure may have changed",
				zap.Int("empty_status_count", emptyStatusCount),
				zap.Int("total_check_calls", totalCheckCalls),
				zap.String("freight_tracking_id", freightID),
				zap.String("tenant", r.tms.Tenant),
			)
		}

		if emptySourceCount > 0 {
			log.WarnNoSentry(
				ctx,
				"check calls with empty source detected - HTML structure may have changed",
				zap.Int("empty_source_count", emptySourceCount),
				zap.Int("total_check_calls", totalCheckCalls),
				zap.String("freight_tracking_id", freightID),
				zap.String("tenant", r.tms.Tenant),
			)
		}

		// Critical alert: if both status and source are empty
		if emptyStatusCount > 0 && emptySourceCount > 0 && !strings.Contains(r.tms.Tenant, "training") {
			log.Error(
				ctx,
				"CRITICAL: check calls missing both status and source - Relay HTML structure likely changed",
				zap.Int("affected_check_calls", totalCheckCalls),
				zap.String("freight_tracking_id", freightID),
				zap.String("tenant", r.tms.Tenant),
			)
		}
	}

	return checkCalls, nil
}

// checkCallParser encapsulates the parsing logic for check calls
type checkCallParser struct {
	loadID    uint
	freightID string
}

// parseCheckCallFromElement extracts check call data from a single HTML element
func (p *checkCallParser) parseCheckCallFromElement(ctx context.Context, s *goquery.Selection) models.CheckCall {
	cc := models.CheckCall{
		LoadID:            p.loadID,
		FreightTrackingID: p.freightID,
	}

	// Extract basic fields
	cc.Status = p.extractStatus(s)
	cc.Source = p.extractSource(s)
	cc.Notes = p.extractNotes(s)
	cc.Author = p.extractAuthor(s)

	// Extract location information
	cc.City, cc.State = p.extractLocation(s)

	// Extract additional info from new tooltip format (not stored in CheckCall struct currently)
	truckNumber, trailerNumber, driverName := p.extractAdditionalInfo(s)
	if truckNumber != "" || trailerNumber != "" || driverName != "" {
		// Log the additional info for debugging/future use
		log.Debug(
			ctx,
			"extracted additional check call info",
			zap.String("freight_tracking_id", p.freightID),
			zap.String("truck_number", truckNumber),
			zap.String("trailer_number", trailerNumber),
			zap.String("driver_name", driverName),
		)
	}

	cc.IsOnTime = p.extractOnTimeFlag(s)
	cc.IsException = p.extractExceptionFlag(s)

	cc.DateTimeWithoutTimezone = p.extractInDateTime(s)
	cc.EndDateTimeWithoutTimezone = p.extractOutDateTime(s)
	cc.NextStopETAWithoutTimezone = p.extractETA(s)
	cc.CapturedDatetime = p.extractCapturedDateTime(s)

	elementText := s.Text()
	truncatedText := elementText
	if len(elementText) > 200 {
		truncatedText = elementText[:200]
	}

	if cc.Status == "" {
		log.WarnNoSentry(
			ctx,
			"failed to extract status from check call HTML element",
			zap.String("freight_tracking_id", p.freightID),
			zap.String("element_html", truncatedText),
		)
	}

	if cc.Source == "" {
		log.WarnNoSentry(
			ctx,
			"failed to extract source from check call HTML element",
			zap.String("freight_tracking_id", p.freightID),
			zap.String("element_html", truncatedText),
		)
	}

	// Log if datetime parsing failed (common indicator of HTML structure changes)
	if !cc.DateTimeWithoutTimezone.Valid {
		log.WarnNoSentry(
			ctx,
			"failed to extract datetime from check call HTML element",
			zap.String("freight_tracking_id", p.freightID),
			zap.String("status", cc.Status),
			zap.String("element_html", truncatedText),
		)
	}

	// Set timezone if we have location info
	if cc.Timezone == "" && cc.City != "" && cc.State != "" {
		if tz, err := timezone.GetTimezone(ctx, cc.City, cc.State, ""); err == nil {
			cc.Timezone = tz
		} else {
			log.WarnNoSentry(ctx, "error looking up check call timezone", zap.Error(err))
		}
	}

	return cc
}

// extractStatus extracts the status from both new and legacy HTML formats
func (p *checkCallParser) extractStatus(s *goquery.Selection) string {
	// Try new format first - look for the badge element with specific classes
	var statusBadge string
	s.Find("div").Each(func(_ int, div *goquery.Selection) {
		if statusBadge != "" {
			return
		}
		classes := div.AttrOr("class", "")
		if strings.Contains(classes, "kt-font-sans") &&
			strings.Contains(classes, "kt-inline-flex") &&
			strings.Contains(classes, "kt-rounded-full") &&
			strings.Contains(classes, "kt-uppercase") {
			statusBadge = strings.TrimSpace(div.Text())
		}
	})

	if statusBadge != "" {
		return statusBadge
	}

	// Try new format with tooltip approach
	if status := p.getTooltipValue(s, "status"); status != "" {
		return status
	}

	// Try legacy format
	if status := strings.TrimSpace(s.Find(".update-title").Text()); status != "" {
		return status
	}

	// Fallback to h3 tag
	return strings.TrimSpace(s.Find("h3").Text())
}

// extractSource extracts the source from both new and legacy HTML formats
func (p *checkCallParser) extractSource(s *goquery.Selection) string {
	// Try new format first - look for "Source" tooltip
	if source := p.getTooltipValueByExactMatch(s, "Source"); source != "" {
		return source
	}

	// Try alternative new format approach
	if source := p.getTooltipValue(s, "source"); source != "" {
		return source
	}

	// Try legacy format
	sourceText := strings.TrimSpace(s.Find(".source.d-inline").Text())
	return strings.TrimSpace(strings.ReplaceAll(sourceText, "Source:", ""))
}

// extractNotes extracts notes from both new and legacy HTML formats
func (p *checkCallParser) extractNotes(s *goquery.Selection) string {
	// Try new format first - look for "Notes" tooltip
	if notes := p.getTooltipValueByExactMatch(s, "Notes"); notes != "" {
		return notes
	}

	// Try alternative new format approach
	if notes := p.getTooltipValue(s, "notes"); notes != "" {
		return notes
	}

	// Try legacy format - check both .notes span and .notes Note:
	if notes := strings.TrimSpace(s.Find(".notes span").Text()); notes != "" {
		return notes
	}

	// Handle "Note:" prefix format
	notesText := strings.TrimSpace(s.Find(".notes").Text())
	if strings.HasPrefix(notesText, "Note:") {
		return strings.TrimSpace(strings.TrimPrefix(notesText, "Note:"))
	}
	if strings.HasPrefix(notesText, "Notes:") {
		return strings.TrimSpace(strings.TrimPrefix(notesText, "Notes:"))
	}

	return notesText
}

// extractAuthor extracts the author from various HTML formats
func (p *checkCallParser) extractAuthor(s *goquery.Selection) string {
	// Try new format first - look for author initials with full name in tooltip
	var author string
	s.Find(".kt-bg-gray-200").Each(func(_ int, elem *goquery.Selection) {
		if author != "" {
			return
		}
		tooltipTarget := elem.Find(".kt-tooltip-target")
		if tooltipTarget.Length() > 0 {
			// Get the full name from tooltip
			fullName := strings.TrimSpace(tooltipTarget.Find(".kt-tooltip-box").Text())
			if fullName != "" && !strings.Contains(fullName, ":") {
				author = fullName
			}
		}
	})

	if author != "" {
		return author
	}

	// Try alternative new format - look for author in tooltip
	s.Find(".kt-bg-gray-200 .kt-tooltip-box").Each(func(_ int, box *goquery.Selection) {
		if author != "" {
			return
		}
		txt := strings.TrimSpace(box.Text())
		if txt != "" && !strings.Contains(txt, ":") {
			author = txt
		}
	})

	if author != "" {
		return author
	}

	// Try legacy format with various prefixes
	userText := strings.TrimSpace(s.Find(".user").Text())
	authorPrefixes := []string{
		"By User:", "Exception Reported By:", "Notes By User:", "Update Captured By User:",
		"Stop Marked Delivered By User:", "Marked Arrived By User:", "Marked Loaded By User:",
	}

	for _, prefix := range authorPrefixes {
		if strings.Contains(userText, prefix) {
			return strings.TrimSpace(strings.Split(userText, prefix)[1])
		}
	}

	return ""
}

// extractLocation extracts city and state from various HTML formats
func (p *checkCallParser) extractLocation(s *goquery.Selection) (city, state string) {
	// Try new format first - look for "Stop" tooltip
	if stopVal := p.getTooltipValue(s, "stop"); stopVal != "" {
		lines := strings.Split(stopVal, "\n")
		for i := len(lines) - 1; i >= 0; i-- {
			line := strings.TrimSpace(lines[i])
			if strings.Contains(line, ",") {
				parts := strings.Split(line, ",")
				return strings.TrimSpace(parts[0]), strings.TrimSpace(parts[1])
			}
		}
	}

	// Try truck location format
	truckLocation := strings.TrimSpace(strings.ReplaceAll(s.Find(".truck-location").Text(), "Truck Location:", ""))
	if truckLocation != "" {
		parts := strings.Split(truckLocation, ",")
		if len(parts) >= 2 {
			return strings.TrimSpace(parts[0]), strings.TrimSpace(parts[1])
		}
		return strings.TrimSpace(parts[0]), ""
	}

	// Try warehouse location format
	warehouseAddr := s.Find(".name span")
	if warehouseAddr.Length() >= 2 {
		return strings.TrimSpace(warehouseAddr.Eq(0).Text()), strings.TrimSpace(warehouseAddr.Eq(1).Text())
	}

	// Try stop sequence format
	if stopSeq := strings.TrimSpace(strings.ReplaceAll(s.Find(".stop-sequence").Text(), ":", "")); stopSeq != "" {
		return stopSeq, ""
	}

	return "", ""
}

// extractAdditionalInfo extracts additional information from new tooltip format
func (p *checkCallParser) extractAdditionalInfo(s *goquery.Selection) (truckNumber, trailerNumber, driverName string) {
	truckNumber = p.getTooltipValueByExactMatch(s, "Truck number")
	trailerNumber = p.getTooltipValueByExactMatch(s, "Trailer number")
	driverName = p.getTooltipValueByExactMatch(s, "Driver name")

	return truckNumber, trailerNumber, driverName
}

// extractOnTimeFlag extracts the on-time boolean flag
func (p *checkCallParser) extractOnTimeFlag(s *goquery.Selection) *bool {
	// Try new format first - look for "On Time:" in tooltip
	var onTimeValue *bool
	s.Find(".kt-tooltip-box").Each(func(_ int, box *goquery.Selection) {
		if onTimeValue != nil {
			return
		}
		txt := strings.TrimSpace(box.Text())
		if strings.HasPrefix(strings.ToLower(txt), "on time:") {
			if idx := strings.Index(txt, ":"); idx != -1 {
				valueStr := strings.TrimSpace(txt[idx+1:])
				value := strings.ToLower(valueStr) == "true"
				onTimeValue = &value
			}
		}
	})

	if onTimeValue != nil {
		return onTimeValue
	}

	// Try alternative new format using getTooltipBoxContaining
	if txt := p.getTooltipBoxContaining(s, "on time"); txt != "" {
		if idx := strings.Index(txt, ":"); idx != -1 {
			value := strings.ToLower(strings.TrimSpace(txt[idx+1:])) == "true"
			return &value
		}
	}

	// Try legacy format
	onTimeText := strings.TrimSpace(strings.ReplaceAll(s.Find(".on-time").Text(), "On Time:", ""))
	if onTimeText != "" {
		value := strings.ToLower(onTimeText) == "true"
		return &value
	}

	return nil
}

// extractExceptionFlag extracts the exception boolean flag
func (p *checkCallParser) extractExceptionFlag(s *goquery.Selection) *bool {
	// Try new format first - look for "Has Issue" in tooltip
	var hasIssue *bool
	s.Find(".kt-tooltip-box").Each(func(_ int, box *goquery.Selection) {
		if hasIssue != nil {
			return
		}
		txt := strings.TrimSpace(box.Text())
		if strings.ToLower(txt) == "has issue" {
			value := true
			hasIssue = &value
		}
	})

	if hasIssue != nil {
		return hasIssue
	}

	// Try alternative new format using getTooltipBoxContaining
	if strings.Contains(strings.ToLower(p.getTooltipBoxContaining(s, "has issue")), "has issue") {
		value := true
		return &value
	}

	// Try legacy format
	exceptionText := strings.TrimSpace(strings.ReplaceAll(s.Find(".has-issue, .issue").Text(), "Issue:", ""))
	if exceptionText != "" {
		value := strings.ToLower(exceptionText) == "true"
		return &value
	}

	return nil
}

// extractInDateTime extracts the in/start datetime from various formats
func (p *checkCallParser) extractInDateTime(s *goquery.Selection) models.NullTime {
	// Try new format first - look for "Truck location time" tooltip
	if timeStr := p.getTooltipValueByExactMatch(s, "Truck location time"); timeStr != "" {
		if dt, err := parseCustomDateTime(timeStr, false, nil); err == nil {
			return dt
		}
	}

	// Try new format - look for "In Datetime" or similar
	if timeStr := p.getTooltipValue(s, "In Datetime"); timeStr != "" {
		if dt, err := parseCustomDateTime(timeStr, false, nil); err == nil {
			return dt
		}
	}

	// Try new format - look for "Delivery Time" tooltip
	if timeStr := p.getTooltipValueByExactMatch(s, "Delivery Time"); timeStr != "" {
		if dt, err := parseCustomDateTime(timeStr, false, nil); err == nil {
			return dt
		}
	}

	// Try legacy format selectors
	selectors := []string{".truck-location-date-time", ".in-date-time", ".delivery-time"}
	prefixes := []string{"Datetime:", "In:", "In Datetime:", "Delivery Time"}

	for _, selector := range selectors {
		if dt := p.extractDateTimeFromSelector(s, selector, prefixes); dt.Valid {
			return dt
		}
	}

	// Try regex fallback on full text
	fullText := s.Text()
	inRE := regexp.MustCompile(`(?i)\b(In|Delivered):\s*(\d{1,2}/\d{1,2}(?:/\d{2,4})?\s*\d{1,2}:\d{2})`)
	if matches := inRE.FindStringSubmatch(fullText); len(matches) > 2 {
		if dt, err := parseCustomDateTime(matches[2], false, nil); err == nil {
			return dt
		}
	}

	// Try generic -time class selector
	return p.extractDateTimeFromGenericSelector(s, false)
}

// extractOutDateTime extracts the out/end datetime
func (p *checkCallParser) extractOutDateTime(s *goquery.Selection) models.NullTime {
	// Try legacy format
	outText := strings.TrimSpace(strings.ReplaceAll(
		strings.ReplaceAll(s.Find(".out-date-time").Text(), "Out:", ""), "Out Datetime:", ""))
	if outText != "" {
		if dt, err := parseCustomDateTime(outText, false, nil); err == nil {
			return dt
		}
	}

	// Try regex fallback
	fullText := s.Text()
	outRE := regexp.MustCompile(`(?i)\bOut:\s*(\d{1,2}/\d{1,2}(?:/\d{2,4})?\s*\d{1,2}:\d{2})`)
	if matches := outRE.FindStringSubmatch(fullText); len(matches) > 1 {
		if dt, err := parseCustomDateTime(matches[1], false, nil); err == nil {
			return dt
		}
	}

	return models.NullTime{}
}

// extractETA extracts the ETA datetime
func (p *checkCallParser) extractETA(s *goquery.Selection) models.NullTime {
	// Try new format first - look for "Estimated Time of Arrival" tooltip
	if timeStr := p.getTooltipValueByExactMatch(s, "Estimated Time of Arrival"); timeStr != "" {
		if dt, err := parseCustomDateTime(timeStr, true, nil); err == nil {
			return dt
		}
	}

	// Try alternative new format - look for "ETA" tooltip
	if timeStr := p.getTooltipValue(s, "ETA"); timeStr != "" {
		if dt, err := parseCustomDateTime(timeStr, true, nil); err == nil {
			return dt
		}
	}

	// Try legacy format
	etaText := strings.TrimSpace(strings.ReplaceAll(s.Find(".eta").Text(), "ETA:", ""))
	if etaText != "" {
		if dt, err := parseCustomDateTime(etaText, true, nil); err == nil {
			return dt
		}
	}

	// Try regex fallback
	fullText := s.Text()
	etaRE := regexp.MustCompile(`(?i)\bETA:\s*(\d{1,2}/\d{1,2}(?:/\d{2,4})?\s*\d{1,2}:\d{2})`)
	if matches := etaRE.FindStringSubmatch(fullText); len(matches) > 1 {
		if dt, err := parseCustomDateTime(matches[1], true, nil); err == nil {
			return dt
		}
	}

	return models.NullTime{}
}

// extractCapturedDateTime extracts the captured datetime
func (p *checkCallParser) extractCapturedDateTime(s *goquery.Selection) models.NullTime {
	// Try new format first - look for "Captured at" tooltip
	if timeStr := p.getTooltipValueByExactMatch(s, "Captured at"); timeStr != "" {
		if dt, err := parseCustomDateTime(timeStr, false, nyLoc); err == nil {
			return dt
		}
	}

	// Try new format - look for "Reported at" tooltip
	if timeStr := p.getTooltipValueByExactMatch(s, "Reported at"); timeStr != "" {
		if dt, err := parseCustomDateTime(timeStr, false, nyLoc); err == nil {
			return dt
		}
	}

	// Try legacy format
	capturedText := strings.TrimSpace(strings.ReplaceAll(
		strings.ReplaceAll(s.Find(".captured-at").Text(), "Captured at:", ""), "Reported at:", ""))
	if capturedText != "" {
		if dt, err := parseCustomDateTime(capturedText, false, nyLoc); err == nil {
			return dt
		}
	}

	// Try regex fallback
	fullText := s.Text()
	capRE := regexp.MustCompile(`(?i)(Captured at|Reported at):\s*(\d{1,2}/\d{1,2}(?:/\d{2,4})?\s*\d{1,2}:\d{2})`)
	if matches := capRE.FindStringSubmatch(fullText); len(matches) > 2 {
		if dt, err := parseCustomDateTime(matches[2], false, nyLoc); err == nil {
			return dt
		}
	}

	return models.NullTime{}
}

// extractDateTimeFromSelector extracts datetime from a specific selector with various prefixes
func (p *checkCallParser) extractDateTimeFromSelector(
	s *goquery.Selection,
	selector string,
	prefixes []string,
) models.NullTime {

	text := strings.TrimSpace(s.Find(selector).Text())
	if text == "" {
		return models.NullTime{}
	}

	// Remove known prefixes
	for _, prefix := range prefixes {
		if strings.HasPrefix(text, prefix) {
			text = strings.TrimSpace(strings.TrimPrefix(text, prefix))
			break
		}
	}

	if text != "" {
		if dt, err := parseCustomDateTime(text, false, nil); err == nil {
			return dt
		}
	}

	return models.NullTime{}
}

// extractDateTimeFromGenericSelector extracts datetime from generic -time class selectors
func (p *checkCallParser) extractDateTimeFromGenericSelector(
	s *goquery.Selection,
	isETA bool,
) models.NullTime {

	timeClassRegex := regexp.MustCompile(`-time\b`)
	dtRegex := regexp.MustCompile(`\b(\d{1,2}/\d{1,2}(/\d{4})? \d{1,2}:\d{2})\b`)

	var result models.NullTime
	s.Find("*").Each(func(_ int, elem *goquery.Selection) {
		if result.Valid {
			return // Already found
		}

		for _, class := range strings.Split(elem.AttrOr("class", ""), " ") {
			if timeClassRegex.MatchString(class) && class != "out-date-time" {
				text := strings.TrimSpace(elem.Text())
				if matches := dtRegex.FindStringSubmatch(text); len(matches) > 1 {
					if dt, err := parseCustomDateTime(matches[1], isETA, nil); err == nil {
						result = dt
						return
					}
				}

			}
		}
	})

	return result
}

// getTooltipValue retrieves the visible value associated with a tooltip (new UI format)
func (p *checkCallParser) getTooltipValue(ev *goquery.Selection, label string) string {
	var val string
	ev.Find(".kt-tooltip-target").Each(func(_ int, tt *goquery.Selection) {
		if val != "" {
			return
		}
		tooltipLabel := strings.TrimSpace(tt.Find(".kt-tooltip-box").Text())
		if strings.Contains(strings.ToLower(tooltipLabel), strings.ToLower(label)) {
			// Extract the value after the colon from the tooltip text
			if idx := strings.Index(tooltipLabel, ":"); idx != -1 {
				val = strings.TrimSpace(tooltipLabel[idx+1:])
			} else {
				// If no colon, get the associated content (the <p> tag content)
				clone := tt.Clone()
				clone.Find(".kt-tooltip-wrapper").Remove()
				clone.Find(".kt-tooltip-box").Remove()
				val = strings.TrimSpace(clone.Find("p").Text())

				// If no <p> tag, fall back to the main content
				if val == "" {
					val = strings.TrimSpace(clone.Text())
				}
			}
		}
	})
	return val
}

// getTooltipValueByExactMatch retrieves value by exact tooltip label match
func (p *checkCallParser) getTooltipValueByExactMatch(ev *goquery.Selection, exactLabel string) string {
	var val string
	ev.Find(".kt-tooltip-target").Each(func(_ int, tt *goquery.Selection) {
		if val != "" {
			return
		}
		tooltipLabel := strings.TrimSpace(tt.Find(".kt-tooltip-box").Text())
		if strings.EqualFold(tooltipLabel, exactLabel) {
			// Get the associated content (the <p> tag content)
			clone := tt.Clone()
			clone.Find(".kt-tooltip-wrapper").Remove()
			clone.Find(".kt-tooltip-box").Remove()
			val = strings.TrimSpace(clone.Find("p").Text())

			// If no <p> tag, fall back to the main content
			if val == "" {
				val = strings.TrimSpace(clone.Text())
			}
		}
	})
	return val
}

// getTooltipBoxContaining retrieves the first tooltip box containing a substring (new UI format)
func (p *checkCallParser) getTooltipBoxContaining(ev *goquery.Selection, substr string) string {
	var txt string
	ev.Find(".kt-tooltip-box").Each(func(_ int, box *goquery.Selection) {
		if txt != "" {
			return
		}
		t := strings.TrimSpace(box.Text())
		if strings.Contains(strings.ToLower(t), strings.ToLower(substr)) {
			txt = t
		}
	})
	return txt
}

// mapReasonToCode maps human-readable reasons to snake_cased codes and returns an error if the reason
// is not recognized. Important for NFI's tracking compliance with customers.
// Late Reason may be empty, depending on the type of check call
func mapReasonToCode(reason string) (string, error) {
	lowerReason := strings.ToLower(reason)

	switch lowerReason {
	case "":
		return "", nil
	case "accident":
		return "accident", nil
	case "consignee related":
		return "consignee_related", nil
	case "driver related":
		return "driver_related", nil
	case "mechanical breakdown":
		return "mechanical_breakdown", nil
	case "other carrier related":
		return "other_carrier_related", nil
	case "previous stop":
		return "previous_stop", nil
	case "shipper related":
		return "shipper_related", nil
	case "holiday":
		return "holiday", nil
	case "weather related", "weather":
		return "weather", nil
	case "return to shipper":
		return "return_to_shipper", nil
	case "driver not available":
		return "driver_not_available", nil
	case "processing delay":
		return "processing_delay", nil
	case "trailer not available":
		return "trailer_not_available", nil
	case "insufficient time to complete delivery":
		return "insufficient_time_to_complete_delivery", nil
	default:
		return "", fmt.Errorf("unrecognized reason: %s", reason)
	}
}

// mapSourceToCode maps human-readable reasons to snake_cased codes and returns an error if the reason
// is not recognized. Important for NFI's tracking compliance with customers.
// Source must always be provided, empty string is not valid
func mapSourceToCode(source string) (string, error) {
	source = strings.ToLower(source)

	switch source {
	case "driver":
		return "driver", nil
	case "web":
		return "web", nil
	case "dispatcher":
		return "dispatcher", nil
	case "shipper/receiver":
		return "shipper_receiver", nil
	default:
		return "", fmt.Errorf("unrecognized source: %s", source)
	}
}
