package llm

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	email_helpers "github.com/drumkitai/drumkit/common/helpers/emails"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3fetcher"
	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/integrations/llm/textract"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/prompts"
	globalRDS "github.com/drumkitai/drumkit/common/rds"
)

//nolint:lll
type (
	Address struct {
		City  string `json:"city" jsonschema_description:"Full city name in titlecase. If only a region was specified (e.g., 'South Jersey'), replace with the main city or recognized location. Can be empty string if no city is provided."`
		State string `json:"state" jsonschema_description:"2-letter state abbreviation in uppercase. May be empty if no state is provided."`
		Zip   string `json:"zip" jsonschema_description:"Valid U.S. zip code or candian postal code of the location. May be empty if not provided."`
	}

	Stop struct {
		Location Address `json:"location" jsonschema_description:"Details of the stop's address (city, state, zip)."`
		DateTime string  `json:"date_time" jsonschema_description:"Date/time for this specific stop in 'mm/dd/yyyy' or 'mm/dd/yyyy, hh:MM+AM/PM' format. May be empty if unknown."`
		Type     string  `json:"type" jsonschema_description:"Specifies whether this stop is 'pickup', 'stop', or 'dropoff' in the overall route."`
	}

	QuoteRequest struct {
		Reasoning string `json:"reasoning" jsonschema_description:"A concise explanation of why the extracted values were selected for the quote request."`
		TruckType string `json:"truck_type" jsonschema_description:"Type of truck needed, one of 'VAN', 'REEFER', 'FLATBED', 'HOTSHOT', 'BOX TRUCK'. Defaults to '' if unclear."`
		Stops     []Stop `json:"stops" jsonschema_description:"Ordered list of all stops (including pickup, any intermediate stops, and final dropoff)."`
	}

	// NOTE: We maintain the response type as array of quote requests instead of only one in case extraction on email
	//       labeled quote request yields multiple quote requests. We don't want to miss those quote requests as they
	//       may be valid.
	//       In the event quote request extraction results in >1 quote requests we log a warning and can update
	//       our quoting labelling prompt to label as batch quote more accurately as needed.
	QuoteRequestOutput struct {
		QuoteRequests []QuoteRequest `json:"quote_requests" jsonschema_description:"Collection of quote requests extracted from the email content."`
	}

	CustomerInfo struct {
		// LLM's parsing/guess of the customer/shipper name
		ShipperName string `json:"shipper_name"`
		// If the email was forwarded, LLM is asked to parse the original sender email
		OriginalSenderEmail string `json:"original_sender_email"`
	}
)

var (
	getZeroxMarkdownFunc       = extractor.GetZeroxMarkdown
	getTimezoneByZipOrCityFunc = timezone.GetTimezoneByZipOrCity
)

type Result struct {
	quotes []models.QuoteRequest
}

func ExtractQuoteRequestSuggestions(
	ctx context.Context,
	email models.Email,
	openaiService openai.Service,
	textractClient textract.Client,
	rds RDSInterface,
	opts ...Option,
) (res []models.QuoteRequest, err error) {

	attrs := otel.EmailAttrs(&email)
	ctx, metaSpan := otel.StartSpan(ctx, "ExtractQuoteRequestSuggestions", attrs)
	defer func() { metaSpan.End(err) }()

	var quoteRequestsFromAttachments []models.QuoteRequest

	// Collect quote requests from PDFs
	if email.HasPDFs {
		quoteRequestsFromAttachments, err = extractQRSuggestionsFromAttachments(
			ctx, email, openaiService, textractClient, rds, opts...,
		)
		if err != nil {
			return res, fmt.Errorf("LLM error extracting quote request from attachments: %w", err)
		}
	}

	if len(quoteRequestsFromAttachments) > 0 {
		return quoteRequestsFromAttachments, nil
	}

	// If no attachments OR no quotes found in attachments, try to extract quotes from email body
	quoteRequestsFromBody, err := promptQuoteRequestLLM(
		ctx,
		email,
		nil,
		models.Attachment{},
		openaiService,
		false,
		rds,
		opts...,
	)

	if err != nil {
		return res, fmt.Errorf("LLM error extracting quote request info: %w", err)
	}

	return quoteRequestsFromBody, nil
}

func extractQRSuggestionsFromAttachments(
	ctx context.Context,
	email models.Email,
	openaiService openai.Service,
	textractClient textract.Client,
	rds RDSInterface,
	opts ...Option,
) (res []models.QuoteRequest, err error) {
	attrs := otel.EmailAttrs(&email)
	ctx, metaSpan := otel.StartSpan(ctx, "extractQRSuggestionsFromAttachments", attrs)
	defer func() { metaSpan.End(err) }()

	var wg sync.WaitGroup
	var allQuotes []models.QuoteRequest

	const maxConcurrent = 5
	sem := make(chan struct{}, maxConcurrent)

	resultChan := make(chan Result, len(email.Attachments))

	// Create S3 client for fetching attachments
	s3Client, err := s3fetcher.New(ctx)
	if err != nil {
		return res, fmt.Errorf("error creating S3 client: %w", err)
	}

	for _, attachment := range email.Attachments {
		sem <- struct{}{}
		wg.Add(1)
		go func(attachment models.Attachment) {
			defer func() {
				<-sem
				wg.Done()
			}()

			ctx = log.With(
				ctx,
				zap.String("attachmentName", attachment.OriginalFileName),
				zap.String("attachmentURL", attachment.S3URL),
			)

			if !email_helpers.IsPDF(attachment.MimeType, attachment.OriginalFileName) {
				log.Info(ctx, "skipping non-PDF attachment", zap.String("attachmentName", attachment.OriginalFileName))
				return
			}

			var attachmentContent any
			url := attachment.S3URL
			fileName := attachment.OriginalFileName

			// download the attachment
			fileData, err := s3Client.FetchObjectFromS3(ctx, attachment.S3URL)
			if err != nil {
				log.WarnNoSentry(ctx, "error fetching file from S3. skipping attachment.", zap.Error(err))
				return
			}

			// Try each PDF conversion method in sequence until one succeeds
			// 1. First try Zerox (preferred method)
			markdown, err := getZeroxMarkdownFunc(ctx, fileName, fileData)
			if err == nil && strings.TrimSpace(markdown) != "" {
				// Happy path - Zerox worked
				attachmentContent = markdown
			} else {
				// 2. If Zerox fails, try GoFitz as fallback
				log.WarnNoSentry(ctx, "conversion to markdown failed, fallback to gofitz", zap.Error(err))

				markdown, err = extractor.S3PDFToMarkdown(fileData)
				if err == nil {
					// GoFitz worked
					attachmentContent = markdown
				} else {
					log.WarnNoSentry(
						ctx,
						"gofitz error, falling back to textract",
						zap.Error(err),
						zap.String("fileName", fileName),
					)

					textractData, err := textract.ExtractData(ctx, attachment.S3URL, textractClient)
					if err != nil {
						log.WarnNoSentry(
							ctx,
							"all conversion methods failed",
							zap.Error(err),
							zap.String("fileName", fileName),
						)
						resultChan <- Result{}
						return
					}

					// Textract worked
					attachmentContent = textractData
				}
			}

			isQR := isQuoteRequestEmail(
				ctx,
				attachmentContent,
				email,
				openaiService,
				true,
			)
			if !isQR {
				log.Info(ctx, "attachment is not a quote request, skipping", zap.String("fileName", fileName))
				resultChan <- Result{}

				return
			}

			quotes, err := promptQuoteRequestLLM(
				ctx,
				email,
				attachmentContent,
				attachment,
				openaiService,
				true,
				rds,
				opts...,
			)
			if err != nil {
				log.WarnNoSentry(ctx,
					"LLM error extracting quote request info:",
					zap.Error(err),
					zap.String("attachmentURL", url),
				)

				return
			}

			// Generate and store embedding for the attachment content
			func() {
				embedding, embErr := openaiService.GetEmbedding(ctx, markdown)
				if embErr != nil {
					log.WarnNoSentry(ctx, "Failed to generate embedding for quote request attachment",
						zap.Error(embErr),
						zap.String("fileName", fileName))
					return
				}

				vectorRepo := globalRDS.GetVectorRepository(ctx)
				if vectorRepo == nil {
					return
				}

				embErr = vectorRepo.StoreAttachmentEmbedding(
					ctx,
					&email,
					attachment.ExternalID,
					embedding,
				)
				if embErr != nil {
					log.WarnNoSentry(ctx, "Failed to store quote request attachment embedding",
						zap.Error(embErr),
						zap.String("fileName", fileName))
					return
				}

				log.Info(ctx, "Successfully stored quote request attachment embedding",
					zap.String("fileName", fileName))

			}()

			resultChan <- Result{quotes: quotes}

		}(attachment)
	}

	go func() {
		wg.Wait()
		close(resultChan)
	}()

	for result := range resultChan {
		if len(result.quotes) > 0 {
			allQuotes = append(allQuotes, result.quotes...)
		}
	}

	return allQuotes, nil

}

func promptQuoteRequestLLM(
	ctx context.Context,
	email models.Email,
	attachmentContent any, // nil, Markdown string or Textract
	attachment models.Attachment,
	openaiService openai.Service,
	hasAttachments bool,
	rds RDSInterface,
	opts ...Option,
) (res []models.QuoteRequest, err error) {

	options := &Options{
		Config: models.QuickQuoteConfig{},
	}

	for _, opt := range opts {
		opt(options)
	}

	integrations, err := rds.GetTMSListByServiceID(ctx, email.ServiceID)
	if err != nil {
		return res, fmt.Errorf("error getting TMS list for service: %w", err)
	}

	var tmsID uint
	if len(integrations) > 0 {
		tmsID = integrations[0].ID
	}

	// Ignore service ID 1 (Drumkit) for this error
	if len(integrations) > 1 && email.ServiceID != 1 {
		log.Warn(
			ctx,
			"multiple TMS integrations found for quote request service, defaulting to first",
			zap.Uint("serviceID", email.ServiceID),
			zap.Int("countTMS", len(integrations)),
		)
	}

	emailID := strconv.FormatUint(uint64(email.ID), 10)

	subject := email.Subject
	// remove subject if we've already used the subject from this thread in a previous email
	emailCount, err := rds.GetNumberOfEmailsByThreadIDAndUserID(ctx, email.ThreadID, email.UserID)
	if err != nil {
		log.WarnNoSentry(ctx, "error getting email count", zap.Error(err))
	}

	if emailCount > 1 {
		subject = ""
		log.Info(ctx, "email subject removed for thread with multiple emails", zap.Int("emailCount", emailCount))
	}

	currentYear := strconv.Itoa(time.Now().Year())

	systemPrompt := strings.ReplaceAll(prompts.QuoteRequestSystemPrompt, "{CURRENT_YEAR}", currentYear)

	var content any = email.BodyWithoutSignature

	// Remove links from email body
	content = RemoveLinksIfString(content)

	// Set attachment content if not nil, and attempt to remove links
	if attachmentContent != nil {
		content = RemoveLinksIfString(attachmentContent)
	}

	estTimezone, err := time.LoadLocation("America/New_York")
	if err != nil {
		log.Warn(ctx, "failed to load America/New_York location, falling back to UTC", zap.Error(err))
		estTimezone = time.UTC
	}

	userPrompt := fmt.Sprintf(
		`
		Email sent at: %s
		Subject: %s
		Email body:
		%s
		`,
		email.SentAt.In(estTimezone).Format("01/02/2006, 03:04PM"),
		subject,
		content,
	)

	response, err := openaiService.GetResponse(
		ctx,
		email,
		attachment,
		braintrustsdk.CreateProjectDetails(braintrustsdk.QRBeginConversation, hasAttachments),
		openai.ResponseOptions{
			DeveloperPrompt: systemPrompt,
			UserPrompt:      userPrompt,
			Schema:          extractor.GenerateSchema[QuoteRequestOutput](),
		},
	)
	if err != nil {
		return res, fmt.Errorf("error getting response from LLM: %w", err)
	}

	btQuoteConversationLogID := response.BraintrustLogID
	result, err := extractor.StructExtractor[QuoteRequestOutput](response.Content)
	if err != nil {
		return res, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	if len(result.QuoteRequests) == 0 {
		log.WarnNoSentry(ctx, "no quote requests found for email:", zap.String("emailID", emailID))
		return res, nil
	}

	mappedCustomer, btCustomerLogID, err := promptLLMForCustomer(
		ctx,
		tmsID,
		email,
		attachmentContent,
		openaiService,
		braintrustsdk.CreateProjectDetails(braintrustsdk.QRGetCustomer, hasAttachments),
		rds,
	)
	if err != nil {
		log.WarnNoSentry(ctx, "error mapping customer", zap.Error(err))
	} else {
		log.Debug(ctx, "mapped customer", zap.Any("customer", mappedCustomer))
	}

	for _, qr := range result.QuoteRequests {
		log.Debug(ctx, "transportType", zap.String("transportType", qr.TruckType))
		stops := make([]models.Stop, len(qr.Stops))

		for i, stop := range qr.Stops {
			dateTime, err := parseDate(
				ctx,
				stop.DateTime,
				Address{
					City:  stop.Location.City,
					State: stop.Location.State,
				},
			)
			if err != nil {
				log.WarnNoSentry(ctx, "error parsing date", zap.Error(err))
			}

			stops[i] = models.Stop{
				StopType:   stop.Type,
				StopNumber: i,
				Order:      i, // For backward compatibility
				Address: models.Address{
					City:  stop.Location.City,
					State: stop.Location.State,
					Zip:   stop.Location.Zip,
				},
			}

			// Set time fields based on stop type
			switch stop.Type {
			case "pickup":
				stops[i].ReadyTime = models.NullTime{
					Time:  dateTime,
					Valid: err == nil && !dateTime.IsZero(),
				}
			case "dropoff":
				stops[i].MustDeliver = models.NullTime{
					Time:  dateTime,
					Valid: err == nil && !dateTime.IsZero(),
				}
			// For intermediate stops, use must deliver
			default:
				stops[i].MustDeliver = models.NullTime{
					Time:  dateTime,
					Valid: err == nil && !dateTime.IsZero(),
				}
			}
		}

		var firstPickup, lastDropoff *models.Stop
		for i := 0; i < len(stops); i++ {
			if stops[i].StopType == "pickup" && firstPickup == nil {
				firstPickup = &stops[i]
			}
		}
		for i := len(stops) - 1; i >= 0; i-- {
			if stops[i].StopType == "dropoff" && lastDropoff == nil {
				lastDropoff = &stops[i]
			}
		}

		var pickupLocation, dropoffLocation models.Address
		var pickupDate, dropoffDate models.NullTime
		if firstPickup != nil {
			pickupLocation = firstPickup.Address
			pickupDate = firstPickup.ReadyTime
		}
		if lastDropoff != nil {
			dropoffLocation = lastDropoff.Address
			dropoffDate = lastDropoff.MustDeliver
		}

		if pickupLocation == (models.Address{}) && dropoffLocation == (models.Address{}) {
			log.Debug(ctx, "Skipping quote request with no pickup or dropoff location in stops")
			continue
		}

		if !isValidQuoteRequest(ctx, userPrompt, &pickupLocation, &dropoffLocation) {
			log.Info(ctx, "LLM hallucinated, skipping quote request",
				zap.Any("pickup", pickupLocation),
				zap.Any("dropoff", dropoffLocation))
			continue
		}

		transportType, err := validateTransportType(
			ctx,
			email,
			attachmentContent,
			models.TransportType(qr.TruckType),
			options.Config,
		)
		if err != nil {
			if options.Config.DefaultTransportType != "" {
				transportType = options.Config.DefaultTransportType
			} else {
				transportType = models.VanTransportType
			}
		}

		suggestedRequest := models.QuoteLoadInfo{
			TransportType:    transportType,
			PickupLocation:   pickupLocation,
			PickupDate:       pickupDate,
			DeliveryLocation: dropoffLocation,
			DeliveryDate:     dropoffDate,
			Stops:            stops,
		}

		if mappedCustomer != nil {
			suggestedRequest.CustomerID = mappedCustomer.ID
			suggestedRequest.Customer = mappedCustomer.CompanyCoreInfo
		}

		res = append(res, models.QuoteRequest{
			UserID:           email.UserID,
			EmailID:          email.ID,
			ThreadID:         email.ThreadID,
			ServiceID:        email.ServiceID,
			RawLLMOutput:     suggestedRequest, // This is the LLM output
			SuggestedRequest: suggestedRequest, // This may be edited by our smart suggestion engine
			Status:           models.Pending,
			Attachment:       attachment,
			SourceCategory:   models.EmailSourceCategory,
			SourceExternalID: email.ExternalID,
			BraintrustLogIDs: createBraintrustQRLogRecordList(btQuoteConversationLogID, btCustomerLogID),
		})
	}

	return res, nil
}

func parseDate(ctx context.Context, ts string, addr Address) (time.Time, error) {
	if ts == "" {
		return time.Time{}, nil
	}

	// Try parsing with time component first
	if t, err := parseWithTimeComponent(ctx, ts, addr); err == nil {
		return t, nil
	}

	// If parsing with time component fails, try parsing date only
	return parseDateOnly(ts)
}

func parseWithTimeComponent(ctx context.Context, ts string, addr Address) (time.Time, error) {
	timezone, err := getTimezoneByZipOrCityFunc(ctx, addr.Zip, addr.City, addr.State, "")
	if err != nil {
		return time.Time{}, err
	}

	loc, err := time.LoadLocation(timezone)
	if err != nil {
		return time.Time{}, err
	}

	// Try parsing "MM/DD/YYYY, h:mmAM" or "MM/DD/YYYY, h:mmPM" format
	return time.ParseInLocation("01/02/2006, 3:04PM", ts, loc)
}

func parseDateOnly(s string) (time.Time, error) {
	dateOnly := strings.Split(s, ",")[0]
	layouts := []string{"01/02/2006", time.DateOnly}

	for _, layout := range layouts {
		if t, err := time.Parse(layout, dateOnly); err == nil {
			// Use a time in UTC that always equals the same day in the American continent
			return time.Date(t.Year(), t.Month(), t.Day(), 16, 0, 0, 0, time.UTC), nil
		}
	}

	return time.Time{}, fmt.Errorf("failed to parse date: %s", s)
}

func isQuoteRequestEmail(
	ctx context.Context,
	content any,
	email models.Email,
	openaiService openai.Service,
	hasAttachments bool,
) bool {

	//nolint:lll
	prompt := `
		You are a freight broker tasked with determining if the following email is requesting quote(s)/rate(s) for a new shipment.
		Review the data and evaluate if it includes the bare minimum information for estimating a quote.
		"Bare minimum" constitutes pickup and dropoff locations (at least at the zip or city level, street address is optional).
		Note that the email may contain multiple requests for quotes.
		If it's not asking for a quote but tendering a new shipment, respond with "No".

		Helpful context:
		Key terms that indicate quote request information may include:

		Quote
		Rate
		Ship to
		Pickup
		Drop off
		Carrier Load Quote
		BOL
		Bill of Lading
		Origin
		Destination
		Delivery
		Equipment
		Transport

		Ask yourself: "Does this data contain the bare minimum of information to estimate a quote?"

		Answer with ONLY "YES" if this is a quote request email or "NO" if it is not.
		`

	userPrompt := fmt.Sprintf(
		`
		Email Content:
		%s
		`,
		content,
	)

	response, err := openaiService.GetResponse(
		ctx,
		email,
		models.Attachment{},
		braintrustsdk.CreateProjectDetails(braintrustsdk.QRIsQREmail, hasAttachments),
		openai.ResponseOptions{
			DeveloperPrompt: prompt,
			UserPrompt:      userPrompt,
		},
	)
	if err != nil {
		return false
	}

	return strings.Contains(strings.ToUpper(response.Content), "YES")
}

func createBraintrustQRLogRecordList(
	btQuoteConversationLogID string,
	btCustomerLogID string,
) models.LogRecordList {
	var recordList models.LogRecordList

	if btQuoteConversationLogID != "" {
		recordList = append(recordList, models.LogRecord{
			ID:              btQuoteConversationLogID,
			ProjectStepName: string(braintrustsdk.QRBeginConversation),
		})
	}

	if btCustomerLogID != "" {
		recordList = append(recordList, models.LogRecord{
			ID:              btCustomerLogID,
			ProjectStepName: string(braintrustsdk.QRGetCustomer),
		})
	}

	return recordList
}

// isValidQuoteRequest checking if the pickup and dropoff locations are hallucinated and mapping zipcodes
// to their corresponding city and state.
func isValidQuoteRequest(ctx context.Context, userPrompt string, pickup, dropoff *models.Address) bool {
	if isHallucination(userPrompt, *pickup, *dropoff) {
		return false
	}

	validateZip(ctx, pickup)
	validateZip(ctx, dropoff)

	return true
}

// LLM may correctly parse zip but hallucinate city and state, so validateZip uses AWS to get the correct city & state.
func validateZip(ctx context.Context, loc *models.Address) {
	if helpers.IsBlank(loc.Zip) {
		return // Zip is optional; assume that LLM parsed city, state correctly
	}

	location, err := helpers.AwsLocationLookup(ctx, "", "", loc.Zip)
	if err != nil {
		log.WarnNoSentry(ctx, "validateZip: error looking up zip", zap.Error(err))
		return
	}

	if len(location.Results) == 0 || location.Results[0].Place == nil {
		log.WarnNoSentry(ctx, "validateZip:no location found for zip", zap.String("zip", loc.Zip))
		// If no location found, clear zipcode to avoid hallucinations and fallback to city, state
		// If both pickup & dropoff are missing zipcode, caller will skip QR suggestion
		loc.Zip = ""

		return
	}

	placeRes := location.Results[0].Place
	if placeRes.Municipality != nil && *placeRes.Municipality != loc.City {
		log.WarnNoSentry(ctx, "LLM hallucinated city, using AWS result",
			zap.String("llmCity", loc.City),
			zap.String("awsCity", *placeRes.Municipality),
		)

		loc.City = *placeRes.Municipality
	}

	if placeRes.Region != nil {
		stateAbbrv := helpers.GetStateAbbreviation(ctx, *placeRes.Region)
		if stateAbbrv == "Unknown State" {
			stateAbbrv = *placeRes.Region
		}

		if loc.State != stateAbbrv {
			log.WarnNoSentry(ctx, "LLM hallucinated state, using AWS result",
				zap.String("llmState", loc.State),
				zap.String("awsState", stateAbbrv),
			)
			loc.State = stateAbbrv
		}
	}
}
