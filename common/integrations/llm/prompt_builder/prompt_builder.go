package promptbuilder

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
	tmsCustomerPromptsDB "github.com/drumkitai/drumkit/common/rds/tms_customer_prompts"
)

// Constants for prompt validation
const (
	// MaxPromptTokens is a conservative limit for GPT-5-nano (400K tokens)
	// Using 100K tokens as a safe limit
	MaxPromptTokens = 100000
)

// Variables to hold function references for mocking
var (
	getActiveCustomerPromptsFunc = tmsCustomerPromptsDB.GetActiveCustomerPrompts
	getActiveTMSPromptsFunc      = tmsCustomerPromptsDB.GetActiveTMSPrompts
)

// BuildPromptForTMS gets the prompt for a TMS-wide extractor
// 1. Default prompt
// 2. TMS-wide prompts (if any)
func BuildPromptForTMS(
	ctx context.Context,
	tmsID uint,
	feature string,
	extractorName string,
	defaultPrompt string,
) (string, error) {
	tmsPrompts, err := getActiveTMSPromptsFunc(ctx, tmsID, feature)
	if err != nil {
		return "", fmt.Errorf("error getting TMS-wide prompts: %w", err)
	}

	// Build composite prompt starting with default
	finalPrompt := defaultPrompt
	hasCustomPrompts := false

	// Append all TMS-wide prompts for this extractor
	for _, prompt := range tmsPrompts {
		if prompt.ExtractorName == extractorName {
			finalPrompt += "\n\n" + prompt.PromptText
			hasCustomPrompts = true
		}
	}

	// Only validate token count if custom prompts were added
	if hasCustomPrompts {
		tokenCount := helpers.CountTokens(finalPrompt)
		if tokenCount > MaxPromptTokens {
			return "", fmt.Errorf("prompt exceeds maximum token count: %d > %d", tokenCount, MaxPromptTokens)
		}
	}

	return finalPrompt, nil
}

// BuildPromptForExtractor resolves the appropriate prompt for a tms, customer and extractor
func BuildPromptForExtractor(
	ctx context.Context,
	tmsID uint,
	customerID uint,
	feature string,
	extractorName string,
	defaultPrompt string,
) (string, error) {

	// Skip prompt resolution entirely if tmsID is 0 (no TMS found)
	if tmsID == 0 {
		return defaultPrompt, nil
	}

	// Always start with default prompt
	finalPrompt := defaultPrompt
	hasCustomPrompts := false

	// Add TMS-wide prompts first (batch fetch)
	tmsPrompts, err := getActiveTMSPromptsFunc(ctx, tmsID, feature)
	if err != nil {
		return "", fmt.Errorf("error getting TMS-wide prompts: %w", err)
	}

	// Append all TMS-wide prompts for this extractor
	for _, prompt := range tmsPrompts {
		if prompt.ExtractorName == extractorName {
			log.Debug(
				ctx, "adding TMS-wide prompt",
				zap.String("extractor", extractorName),
				zap.Uint("tmsID", tmsID),
			)
			finalPrompt += "\n\n" + prompt.PromptText
			hasCustomPrompts = true
		}
	}

	// Add customer-specific prompts if customerID is not 0
	if customerID != 0 {
		// Add customer-specific prompts (batch fetch)
		customerPrompts, err := getActiveCustomerPromptsFunc(ctx, tmsID, customerID, feature)
		if err != nil {
			return "", fmt.Errorf("error getting customer prompts: %w", err)
		}

		// Append all customer-specific prompts for this extractor
		for _, prompt := range customerPrompts {
			if prompt.ExtractorName == extractorName {
				log.Debug(
					ctx, "adding customer-specific prompt",
					zap.String("extractor", extractorName),
					zap.Uint("customerID", customerID),
				)
				finalPrompt += "\n\n" + prompt.PromptText
				hasCustomPrompts = true
			}
		}
	}

	// Only validate token count if custom prompts were added
	if hasCustomPrompts {
		tokenCount := helpers.CountTokens(finalPrompt)
		if tokenCount > MaxPromptTokens {
			return "", fmt.Errorf("prompt exceeds maximum token count: %d > %d", tokenCount, MaxPromptTokens)
		}
	}

	return finalPrompt, nil
}
