package dat

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
)

// Redis keys last 30 minutes in order to match the DAT Token duration
const redisKeyTTL = 30 * time.Minute

type SerializableDATClient struct {
	IdentityHost  string
	AnalyticsHost string
	UserEmail     string
	OrgToken      ClientToken
	UserToken     ClientToken
}

func redisClientKey(datEmailAddress string) string {
	return fmt.Sprintf("dat-email-%s", strings.ToLower(datEmailAddress))
}

func retrieveRedisClient(ctx context.Context, datEmailAddress string) *Client {
	sDAT, found, err := redis.GetKey[SerializableDATClient](ctx, redisClientKey(datEmailAddress))
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "failed to get DAT client from Redis", zap.Error(err))
	}

	if found {
		client := &Client{
			identityHost:  sDAT.IdentityHost,
			analyticsHost: sDAT.AnalyticsHost,
			userEmail:     sDAT.UserEmail,
			orgToken:      sDAT.OrgToken,
			userToken:     sDAT.UserToken,
		}

		log.Info(ctx, "re-using existing DAT client")

		return client
	}

	return nil
}

func (d *Client) cacheClient(ctx context.Context) {
	sDAT := SerializableDATClient{
		IdentityHost:  d.identityHost,
		AnalyticsHost: d.analyticsHost,
		UserEmail:     d.userEmail,
		OrgToken:      d.orgToken,
		UserToken:     d.userToken,
	}

	redisKey := redisClientKey(d.userEmail)
	err := redis.SetKey(ctx, redisKey, sDAT, redisKeyTTL)
	if err != nil {
		log.Warn(ctx, "failed to set DAT client in Redis", zap.Error(err))
	}
}
