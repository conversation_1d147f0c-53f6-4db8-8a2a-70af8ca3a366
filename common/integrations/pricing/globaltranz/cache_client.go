package globaltranz

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

const globaltranzAuthTokenTTL = 40 * time.Minute

func redisClientKey(serviceID uint, integrationID uint) string {
	return fmt.Sprintf("globaltranz-service-%d-integration-%d", serviceID, integrationID)
}

type SerializableGlobalTranzClient struct {
	AccessToken     string
	Integration     models.Integration
	AuthHost        string
	TMSHost         string
	AddressBookHost string
	MainSystemHost  string
}

func getRedisClient(ctx context.Context, serviceID uint, integrationID uint) *Client {
	sGT, found, err := redis.GetKey[SerializableGlobalTranzClient](ctx, redisClientKey(serviceID, integrationID))
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.WarnNoSentry(ctx, "failed to get globaltranz client from Redis", zap.Error(err))
	}

	if found {
		client := &Client{
			accessToken:     sGT.AccessToken,
			integration:     sGT.Integration,
			authHost:        sGT.AuthHost,
			tmsHost:         sGT.TMSHost,
			addressBookHost: sGT.AddressBookHost,
			mainSystemHost:  sGT.MainSystemHost,
		}

		log.Info(ctx, "re-using existing globaltranz client")
		return client
	}

	return nil
}

func (gt *Client) cacheClient(ctx context.Context) {
	sGT := SerializableGlobalTranzClient{
		AccessToken:     gt.accessToken,
		Integration:     gt.integration,
		AuthHost:        gt.authHost,
		TMSHost:         gt.tmsHost,
		AddressBookHost: gt.addressBookHost,
		MainSystemHost:  gt.mainSystemHost,
	}

	redisKey := redisClientKey(gt.integration.ServiceID, gt.integration.ID)
	err := redis.SetKey(ctx, redisKey, sGT, globaltranzAuthTokenTTL)
	if err != nil {
		log.Warn(ctx, "failed to set globaltranz client in Redis", zap.Error(err))
	}
}
