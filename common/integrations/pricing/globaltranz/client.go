package globaltranz

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
	"go.uber.org/zap"
	"golang.org/x/oauth2"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

type (
	API interface {
		GetIntegrationModel() models.Integration
		GetOrderDetails(ctx context.Context, orderBK string) (*OrderDetails, error)
		GetLaneHistory(ctx context.Context, req *GetLaneHistoryRequest) (*GetLaneHistoryResponse, error)
	}

	Client struct {
		accessToken string
		integration models.Integration
		// List of hosts used throughout auth and fetching data
		authHost        string
		tmsHost         string
		addressBookHost string
		mainSystemHost  string
	}
)

const (
	GetOrderDetailsPath   = "api/tms-order/Order/GetOrderDetails"
	GetCarrierHistoryPath = "api/v1/Vendor/History"
	GetLocationSearchPath = "AddressBook/SearchLocation"
)

var globalTranzBackoffSchedule = []time.Duration{
	10 * time.Second,
	30 * time.Second,
}

func New(ctx context.Context, integration models.Integration) (API, models.PricingOnBoardResp, error) {
	// Try to retrieve an existing client
	cachedClient := getRedisClient(ctx, integration.ServiceID, integration.ID)
	if cachedClient != nil &&
		cachedClient.accessToken != "" &&
		cachedClient.GetIntegrationModel().Username == integration.Username &&
		time.Now().Before(cachedClient.GetIntegrationModel().AccessTokenExpirationDate.Time) {

		log.Info(ctx, "re-using existing globaltranz client")

		// If client is cached, it has already been onboarded
		return cachedClient, models.PricingOnBoardResp{}, nil
	}

	username := integration.Username
	password := integration.EncryptedPassword

	if username == "" || password == nil {
		return nil, models.PricingOnBoardResp{}, errors.New("missing GlobalTranz username or password")
	}

	var gtClient Client
	gtClient.integration = integration
	gtClient.authHost = "is.globaltranz.com"
	gtClient.tmsHost = "tms.globaltranz.com"
	gtClient.addressBookHost = "gtz-wus-tms-addressbook-api-pd.azurewebsites.net"
	gtClient.mainSystemHost = "gtz-wus-tms-systemadmin-api-pd.azurewebsites.net"

	tokenData, err := gtClient.Authenticate(ctx, integration.Tenant)
	if err != nil {
		log.Error(ctx, "Could not authenticate GlobalTranz client", zap.Error(err))
		return nil,
			models.PricingOnBoardResp{},
			fmt.Errorf("error authenticating globaltranz for new client: %w", err)
	}

	gtClient.accessToken = tokenData.AccessToken

	integration.AccessToken = tokenData.AccessToken
	integration.AccessTokenExpirationDate = tokenData.AccessTokenExpirationDate

	log.Info(ctx, "successfully created GlobalTranz client", zap.String("username", username))

	gtClient.cacheClient(ctx)

	// If integration doesn't exist on DB, it's being onboarded. Return onboarding response
	if integration.ID == 0 {
		onBoardResp := models.PricingOnBoardResp{
			Username:                  username,
			AccessToken:               tokenData.AccessToken,
			AccessTokenExpirationDate: tokenData.AccessTokenExpirationDate,
			EncryptedPassword:         password,
		}

		return &gtClient, onBoardResp, nil
	}

	// Otherwise simply update integration record with new access token
	if errUpdate := integrationDB.Update(ctx, &integration); errUpdate != nil {
		log.ErrorNoSentry(
			ctx,
			"failed to update globaltranz token on integrations table",
			zap.Any("integration", integration),
		)
		return nil, models.PricingOnBoardResp{}, fmt.Errorf("integration update failed: %w", errUpdate)
	}

	return &gtClient, models.PricingOnBoardResp{}, nil
}

func (gt *Client) GetIntegrationModel() models.Integration {
	return gt.integration
}

func (gt *Client) post(
	ctx context.Context,
	host string,
	path string,
	body io.Reader,
	out any,
) (helpers.APIResponse, error) {
	return gt.doWithRetry(ctx, http.MethodPost, host, path, nil, body, out)
}

func (gt *Client) doWithRetry(
	ctx context.Context,
	method,
	host string,
	path string,
	query url.Values,
	reqBody io.Reader,
	out any,
) (helpers.APIResponse, error) {
	reqURL := (&url.URL{
		Scheme:   "https",
		Host:     host,
		Path:     path,
		RawQuery: query.Encode(),
	}).String()

	req, err := http.NewRequestWithContext(ctx, method, reqURL, reqBody)
	if err != nil {
		return helpers.APIResponse{Status: 599, Body: ""},
			fmt.Errorf("failed to build %s %s request: %w", method, reqURL, err)
	}

	req.Header.Add("Authorization", "Bearer "+gt.accessToken)

	if method == http.MethodPost {
		req.Header.Set("Content-Type", "application/json")
	}

	httpClient := otel.TracingHTTPClient()
	httpTransport := &http.Transport{
		//nolint:gosec // Workaround for GlobalTranz Identity Server certificate issues
		TLSClientConfig:       &tls.Config{InsecureSkipVerify: true},
		Proxy:                 http.ProxyFromEnvironment,
		ForceAttemptHTTP2:     true,
		MaxIdleConns:          100,
		IdleConnTimeout:       60 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}

	httpClient.Transport = otelhttp.NewTransport(
		httpTransport,
		otelhttp.WithSpanNameFormatter(otel.CustomSpanNameFormatter),
	)

	var apiResponse helpers.APIResponse
	var apiError error
	for _, backoff := range globalTranzBackoffSchedule {
		apiResponse, apiError = gt.do(ctx, httpClient, req, out)

		if apiError == nil {
			break
		}

		if apiResponse.Status == 401 {
			log.Info(ctx, "failed to authenticate with GlobalTranz, refreshing token and retrying")

			if err = gt.RefreshToken(ctx); err != nil {
				log.Error(ctx, "failed to refresh GlobalTranz token", zap.Error(err))
			}
		}

		time.Sleep(backoff)
	}

	return apiResponse, apiError
}

func (gt *Client) do(
	ctx context.Context,
	httpClient *http.Client,
	req *http.Request,
	out any,
) (helpers.APIResponse, error) {
	if err := gt.rateLimitCheck(ctx); err != nil {
		return helpers.APIResponse{
				Status: 429,
				Body:   "Redis rate limit exceeded",
			},
			fmt.Errorf("rate limit check failed: %w", err)
	}

	resp, err := httpClient.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, gt.integration, err)

		var oauthError *oauth2.RetrieveError
		if errors.As(err, &oauthError) {
			return helpers.APIResponse{Status: 401, Body: ""},
				fmt.Errorf("failed to send %s %s request: %w", req.Method, req.URL, err)
		}

		return helpers.APIResponse{Status: 599, Body: ""},
			fmt.Errorf("failed to send %s %s request: %w", req.Method, req.URL, err)
	}
	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, gt.integration, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return helpers.APIResponse{Status: 599, Body: ""},
			fmt.Errorf("failed to read %s response body: %w", req.URL, err)
	}

	if code := resp.StatusCode; code != http.StatusOK && code != http.StatusCreated {
		return helpers.APIResponse{Status: resp.StatusCode, Body: string(body)},
			errtypes.NewHTTPResponseError(gt.integration, req, resp, body)
	}

	if out != nil {
		if err := json.Unmarshal(body, out); err != nil {
			log.Error(ctx, "json unmarshal failed for GlobalTranz response body",
				zap.ByteString("body", body))

			return helpers.APIResponse{Status: resp.StatusCode, Body: string(body)},
				fmt.Errorf("%s %s json unmarshal failed: %w", req.Method, req.URL, err)
		}

		log.Debug(ctx, "received GlobalTranz response",
			zap.String("method", req.Method), zap.String("url", req.URL.String()), zap.Any("body", out))
	}

	return helpers.APIResponse{Status: resp.StatusCode, Body: string(body)}, nil
}

func (gt *Client) rateLimitCheck(ctx context.Context) error {
	config := helpers.RateLimitConfig{
		MaxRequests:    600,
		TimeWindow:     1 * time.Minute,
		RedisKeyPrefix: "globaltranz", // Same key prefix as GlobalTranz pricing
		Tenant:         gt.integration.Tenant,
	}

	return helpers.CheckRateLimit(ctx, config)
}
