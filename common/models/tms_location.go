package models

import (
	"context"
	"strings"

	"github.com/lib/pq"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// SRID for WGS84 coordinate system (standard for GPS)
const SRID = 4326

// Point struct is used to represent latitude and longitude values as type `geometry(Point,4326)` in Postgres DB.
// as type `geometry(Point,4326)` in Postgres DB.

type Point struct {
	Latitude, Longitude float32
}

// Implement gorm.Valuer interface so Go<PERSON> automatically inserts `Point` struct as `geometry(Point, 4326)` type
// using ST_MakePoint function (https://postgis.net/docs/manual-3.3/ST_MakePoint.html)
// Note that X = Longitude and Y = Latitude,
// and that (0,0) is represented as 'POINT(0,0), *not* 'POINT EMPTY'
func (p Point) GormValue(_ context.Context, _ *gorm.DB) clause.Expr {
	return clause.Expr{
		SQL:  "ST_SetSRID(ST_MakePoint(?,?),?)",
		Vars: []any{p.Longitude, p.Latitude, SRID},
	}
}

// Gorm requires Scan function for custom data types to convert from PSQL value to Go struct.
// But the server is designed to query Location.Latitude and Location.Longitude directly,
// so we implement a dummy version of Scan() to satisfy Gorm reqs.
func (p Point) Scan(any) error {
	return nil
}

// Pickup/Dropoff Locations
// Unique indices created on rds/migrate.go due to composite unique index on TMS ID and nested External TMS ID
type TMSLocation struct {
	gorm.Model
	TMSIntegrationID uint        `json:"tmsIntegrationId,omitempty"`
	TMSIntegration   Integration `gorm:"foreignKey:TMSIntegrationID" json:"-"`
	// Belongs to relationship with TMSCarrier
	TMSCarrierID uint       `json:"tmsCarrierId,omitempty"`
	TMSCarrier   TMSCarrier `json:"carrier,omitempty"`
	CompanyCoreInfo
	// Email list bc there can be multiple emails for a single location in a TMS
	// (singular email column still present/populated from CompanyCoreInfo)
	Emails pq.StringArray `gorm:"type:text[]" json:"emails"`
	// e.g. {AddressLine1}, {AddressLine2}, {City}, {State}, {ZipCode}
	// Similar to Warehouse.FullAddress, defined for optimizing SQL queries instead of constantly concatenating
	// tms.GetLocations() MUST assign this field
	FullAddress string  `json:"fullAddress"`
	NameAddress string  `json:"nameAddress"`
	Latitude    float64 `json:"latitude,omitempty"`
	Longitude   float64 `json:"longitude,omitempty"`
	// Point stores coordinates as PostGIS types for efficient geospatial computations
	Point        Point `gorm:"type:geometry(Point,4326)" json:"-"`
	ApptRequired bool  `json:"apptRequired"`
	// e.g. No (un)loading, driver load, drop & hook trailer. Primarily for Mcleod
	DriverLoadingResponsibility string `json:"driverLoadingResponsibility"`
	IsShipper                   bool   `json:"isShipper"`
	IsConsignee                 bool   `json:"isConsignee"`
	Notes                       string `json:"notes"`
	// Customer address specific fields
	Instructions   string         `json:"instructions"`
	Note           string         `json:"note"`
	RefNumber      string         `json:"refNumber"`
	LocationType   string         `json:"locationType"`
	OpenTime       string         `json:"openTime"`
	CloseTime      string         `json:"closeTime"`
	IsTSACompliant bool           `json:"isTSACompliant"`
	Accessorials   pq.StringArray `gorm:"type:text[]" json:"accessorials"`
	Timezone       string         `json:"timezone"`
}

// LocationWithDistance embeds TMSLocation and adds a distance field for radius search results
type LocationWithDistance struct {
	TMSLocation
	MilesDistance float32 `json:"milesDistance,omitempty"`
}

// ConcatAddress returns a string representation of the location's address, ignoring empty field (e.g. AddressLine2)
func ConcatAddress(loc CompanyCoreInfo) string {
	parts := []string{}

	if loc.AddressLine1 != "" {
		parts = append(parts, loc.AddressLine1)
	}
	if loc.AddressLine2 != "" {
		parts = append(parts, loc.AddressLine2)
	}
	if loc.City != "" {
		parts = append(parts, loc.City)
	}
	if loc.State != "" {
		parts = append(parts, loc.State)
	}
	if loc.Zipcode != "" {
		parts = append(parts, loc.Zipcode)
	}

	return strings.Join(parts, ", ")
}
