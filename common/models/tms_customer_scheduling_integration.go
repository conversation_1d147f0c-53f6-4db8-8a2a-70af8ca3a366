package models

import (
	"time"

	"gorm.io/gorm"
)

// TMSCustomerSchedulingIntegration represents the many-to-many association
// between TMS customers and scheduling integrations.
// This allows us to remember which scheduling integration a customer prefers to use.
type TMSCustomerSchedulingIntegration struct {
	gorm.Model

	// Foreign key to TMSCustomer
	TMSCustomerID uint        `gorm:"index;not null" json:"tmsCustomerID"`
	TMSCustomer   TMSCustomer `gorm:"foreignKey:TMSCustomerID" json:"-"`

	// Foreign key to Integration (scheduling type)
	SchedulingIntegrationID uint        `gorm:"index;not null" json:"schedulingIntegrationID"`
	SchedulingIntegration   Integration `gorm:"foreignKey:SchedulingIntegrationID" json:"-"`

	// Metadata about the association
	IsPreferred bool      `gorm:"default:false" json:"isPreferred"` // Whether this is the preferred integration for this customer
	LastUsedAt  time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"lastUsedAt"`
	UsageCount  int       `gorm:"default:1" json:"usageCount"` // How many times this combination has been used

	// Optional notes about why this association exists
	Notes string `json:"notes,omitempty"`

	// Who created this association
	CreatedByUserID uint `gorm:"index" json:"createdByUserID,omitempty"`
	CreatedByUser   User `gorm:"foreignKey:CreatedByUserID" json:"-"`
}

// TableName returns the table name for this model
func (TMSCustomerSchedulingIntegration) TableName() string {
	return "tms_customer_scheduling_integrations"
}
