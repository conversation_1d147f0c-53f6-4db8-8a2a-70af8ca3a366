package models

import "time"

// TimePreference represents the available time slots that can be requested for an appointment.
type TimePreference string

const (
	TimePreferenceAnytime    TimePreference = "Anytime"
	TimePreferenceBeforeNoon TimePreference = "Before Noon"
	TimePreferenceNoonToSix  TimePreference = "Noon - 6pm"
	TimePreferenceAfterSix   TimePreference = "After 6pm"
)

// SchedulingOptions contains configuration for appointment scheduling requests.
type SchedulingOptions struct {
	RequestedDate   time.Time
	TimePreference  TimePreference
	Warehouse       Warehouse
	RequestType     RequestType
	SchedulePID     string
	LoadTypePID     int
	AppointmentType string
	Company         string
	Operation       string
	City            string
	State           string
	ZipCode         string
	Country         string
	CarrierSCAC     string
	// NOTE: Start of Costco specific options
	ProNumber       string
	DoorType        string
	UnloadType      string
	Commodity       string
	ContainerNumber string
	LinkLoadID      string
	Notes           string
	DepotValue      string
	Uom             string
	QtyCount        int
	// End of Costco specific options
	// NOTE: Start of Opendock specific options
	UploadedKeys []string
	Tenant       string
	// End of Opendock specific options
}

// SchedulingOption defines a function that modifies SchedulingOptions.
type SchedulingOption func(*SchedulingOptions)

// Apply updates SchedulingOptions with the provided options.
func (o *SchedulingOptions) Apply(opts ...SchedulingOption) {
	for _, opt := range opts {
		opt(o)
	}
}

// WithTimePreference sets the preferred time slot for the appointment.
// NOTE: These specific values are specific to Retalix's scheduling system.
// Valid preferences are defined by the TimePreference constants:
// - TimePreferenceAnytime: No specific time preference
// - TimePreferenceBeforeNoon: Before 12:00 PM
// - TimePreferenceNoonToSix: Between 12:00 PM and 6:00 PM
// - TimePreferenceAfterSix: After 6:00 PM
func WithTimePreference(tp TimePreference) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.TimePreference = tp
	}
}

// WithRequestedDate sets the desired date for the appointment. The date should be provided in local time and will be
// formatted appropriately for the scheduling request.
func WithRequestedDate(rd time.Time) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.RequestedDate = rd
	}
}

// WithWarehouse sets the target warehouse for the appointment.
func WithWarehouse(wh Warehouse) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.Warehouse = wh
	}
}

// WithRequestType sets the stop type of the warehouse for the appointment.
func WithRequestType(rt RequestType) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.RequestType = rt
	}
}

// WithSchedulePID sets the open slot's ID.
// NOTE: This is a specific value for YardView's API.
func WithSchedulePID(pid string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.SchedulePID = pid
	}
}

// WithAppointmentType sets the appointment type of the warehouse for the appointment.
func WithAppointmentType(at string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.AppointmentType = at
	}
}

func WithCompany(c string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.Company = c
	}
}

func WithOperation(op string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.Operation = op
	}
}

func WithCity(city string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.City = city
	}
}

func WithState(state string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.State = state
	}
}

func WithZipCode(zip string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.ZipCode = zip
	}
}

func WithCountry(country string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.Country = country
	}
}

func WithTenant(tenant string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.Tenant = tenant
	}
}

// WithCarrierSCAC sets the standard carrier alpha code for the appointment.
// NOTE: This is a specific value for YardView's API.
func WithCarrierSCAC(scac string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.CarrierSCAC = scac
	}
}

// WithLoadTypePID sets the load type for the appointment.
// NOTE: This is a specific value for YardView's API.
func WithLoadTypePID(pid int) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.LoadTypePID = pid
	}
}

// NOTE: This is a specific value for Costco
func WithProNumber(proNumber string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.ProNumber = proNumber
	}
}

// NOTE: This is a specific value for Costco
func WithDoorType(doorType string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.DoorType = doorType
	}
}

// NOTE: This is a specific value for Costco
func WithUnloadType(unloadType string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.UnloadType = unloadType
	}
}

// NOTE: This is a specific value for Costco
func WithCommodity(commodity string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.Commodity = commodity
	}
}

// NOTE: This is a specific value for Costco
func WithContainerNumber(containerNumber string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.ContainerNumber = containerNumber
	}
}

// NOTE: This is a specific value for Costco
func WithLinkLoadID(linkLoadID string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.LinkLoadID = linkLoadID
	}
}

// NOTE: This is a specific value for Costco
func WithNotes(notes string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.Notes = notes
	}
}

// NOTE: This is a specific value for Costco
func WithDepotValue(depotValue string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.DepotValue = depotValue
	}
}

// NOTE: This is a specific value for Costco
func WithUom(uom string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.Uom = uom
	}
}

// NOTE: This is a specific value for Costco
func WithQtyCount(qtyCount int) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.QtyCount = qtyCount
	}
}

// WithUploadedKeys sets pre-uploaded Opendock storage keys for multiple file support
// NOTE: This is a specific value for Opendock
func WithUploadedKeys(keys []string) SchedulingOption {
	return func(o *SchedulingOptions) {
		o.UploadedKeys = keys
	}
}
