package emails

import (
	"context"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// Funcs defined here instead of in emails package to avoid circular dependency
// in common/emails and common/integrations/llm
func ShouldProcessAttachments(service *models.Service) bool {
	// Service ID is 0 when processing on-prem, so by default we process those attachments
	return service.ID == 0 ||
		service.IsLoadBuildingEnabled ||
		service.IsQuickQuoteEnabled ||
		service.IsCarrierNetworkQuotingEnabled
}

// IsPDF returns true if the attachment is a PDF or if the content type is octet-stream and the file name ends with .pdf
func IsPDF(contentType, fileName string) bool {
	contentType = strings.ToLower(contentType)
	fileName = strings.ToLower(fileName)

	return strings.Contains(contentType, "pdf") ||
		(strings.Contains(contentType, "octet-stream") && strings.HasSuffix(fileName, ".pdf"))
}

// sanitizeString removes null bytes from a string
func sanitizeString(s string) string {
	return strings.ReplaceAll(s, "\x00", "")
}

// SanitizeEmail removes null bytes from all string fields in an email.
func SanitizeEmail(ctx context.Context, email *models.Email) *models.Email {
	ctx, metaSpan := otel.StartSpan(ctx, "SanitizeEmail", nil)
	defer func() { metaSpan.End(nil) }()

	sanitized := false
	sanitizedFields := []string{}

	// Sanitize all string fields
	if email.Body != "" {
		cleaned := sanitizeString(email.Body)
		if cleaned != email.Body {
			email.Body = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "Body")
		}
	}

	if email.Subject != "" {
		cleaned := sanitizeString(email.Subject)
		if cleaned != email.Subject {
			email.Subject = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "Subject")
		}
	}

	if email.Sender != "" {
		cleaned := sanitizeString(email.Sender)
		if cleaned != email.Sender {
			email.Sender = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "Sender")
		}
	}

	if email.Recipients != "" {
		cleaned := sanitizeString(email.Recipients)
		if cleaned != email.Recipients {
			email.Recipients = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "Recipients")
		}
	}

	if email.CC != "" {
		cleaned := sanitizeString(email.CC)
		if cleaned != email.CC {
			email.CC = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "CC")
		}
	}

	if email.BodyWithoutSignature != "" {
		cleaned := sanitizeString(email.BodyWithoutSignature)
		if cleaned != email.BodyWithoutSignature {
			email.BodyWithoutSignature = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "BodyWithoutSignature")
		}
	}

	if email.Signature != "" {
		cleaned := sanitizeString(email.Signature)
		if cleaned != email.Signature {
			email.Signature = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "Signature")
		}
	}

	if email.ThreadReferences != "" {
		cleaned := sanitizeString(email.ThreadReferences)
		if cleaned != email.ThreadReferences {
			email.ThreadReferences = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "ThreadReferences")
		}
	}

	if email.InReplyTo != "" {
		cleaned := sanitizeString(email.InReplyTo)
		if cleaned != email.InReplyTo {
			email.InReplyTo = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "InReplyTo")
		}
	}

	if email.WebLink != "" {
		cleaned := sanitizeString(email.WebLink)
		if cleaned != email.WebLink {
			email.WebLink = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "WebLink")
		}
	}

	if email.S3URL != "" {
		cleaned := sanitizeString(email.S3URL)
		if cleaned != email.S3URL {
			email.S3URL = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "S3URL")
		}
	}

	if email.Labels != "" {
		cleaned := sanitizeString(email.Labels)
		if cleaned != email.Labels {
			email.Labels = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "Labels")
		}
	}

	if email.CategoryReasoning != "" {
		cleaned := sanitizeString(email.CategoryReasoning)
		if cleaned != email.CategoryReasoning {
			email.CategoryReasoning = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "CategoryReasoning")
		}
	}

	if email.LabelReasoning != "" {
		cleaned := sanitizeString(email.LabelReasoning)
		if cleaned != email.LabelReasoning {
			email.LabelReasoning = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "LabelReasoning")
		}
	}

	if email.RFCMessageID != "" {
		cleaned := sanitizeString(email.RFCMessageID)
		if cleaned != email.RFCMessageID {
			email.RFCMessageID = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "RFCMessageID")
		}
	}

	if email.ExternalID != "" {
		cleaned := sanitizeString(email.ExternalID)
		if cleaned != email.ExternalID {
			email.ExternalID = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "ExternalID")
		}
	}

	if email.ThreadID != "" {
		cleaned := sanitizeString(email.ThreadID)
		if cleaned != email.ThreadID {
			email.ThreadID = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "ThreadID")
		}
	}

	if email.Account != "" {
		cleaned := sanitizeString(email.Account)
		if cleaned != email.Account {
			email.Account = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "Account")
		}
	}

	if sanitized {
		log.Info(
			ctx,
			"Sanitized Email: removed invalid characters",
			zap.Strings("sanitizedFields", sanitizedFields),
		)
	}

	return email
}

// SanitizeOnPremEmail removes null bytes from all string fields in an OnPremEmail.
func SanitizeOnPremEmail(ctx context.Context, email *models.OnPremEmail) *models.OnPremEmail {
	ctx, metaSpan := otel.StartSpan(ctx, "SanitizeOnPremEmail", nil)
	defer func() { metaSpan.End(nil) }()

	sanitized := false
	sanitizedFields := []string{}

	// Sanitize all string fields
	if email.Account != "" {
		cleaned := sanitizeString(email.Account)
		if cleaned != email.Account {
			email.Account = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "Account")
		}
	}

	if email.RFCMessageID != "" {
		cleaned := sanitizeString(email.RFCMessageID)
		if cleaned != email.RFCMessageID {
			email.RFCMessageID = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "RFCMessageID")
		}
	}

	if email.ExternalID != "" {
		cleaned := sanitizeString(email.ExternalID)
		if cleaned != email.ExternalID {
			email.ExternalID = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "ExternalID")
		}
	}

	if email.ThreadID != "" {
		cleaned := sanitizeString(email.ThreadID)
		if cleaned != email.ThreadID {
			email.ThreadID = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "ThreadID")
		}
	}

	if email.ThreadReferences != "" {
		cleaned := sanitizeString(email.ThreadReferences)
		if cleaned != email.ThreadReferences {
			email.ThreadReferences = cleaned
			sanitized = true
			sanitizedFields = append(sanitizedFields, "ThreadReferences")
		}
	}

	if sanitized {
		log.Info(
			ctx,
			"Sanitized OnPremEmail: removed invalid characters",
			zap.Strings("sanitizedFields", sanitizedFields),
		)
	}

	return email
}
