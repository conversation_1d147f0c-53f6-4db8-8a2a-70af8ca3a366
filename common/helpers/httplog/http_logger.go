package httplog

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"

	"go.uber.org/zap"
	"golang.org/x/oauth2"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/redis"
)

// CloudWatch metric filters generate custom metrics from this data.
type IntegrationResponseCode struct {
	AxleIntegrationID uint                   `json:"axleIntegrationID"`
	IntegrationName   models.IntegrationName `json:"integrationName"`
	IntegrationType   models.IntegrationType `json:"integrationType"`
	StatusCode        int                    `json:"statusCode"`
}

func redisUnauthorizedIntegrationAttemptsKey(integrationID uint) string {
	return fmt.Sprintf("integration-%d-unauthorized-attempts", integrationID)
}

func LogHTTPResponseCode(ctx context.Context, integration models.Integration, statusCode int) {

	// Ingestion mocks Gmail and Outlook clients as Integrations for logging, but they won't ever be disabled.
	if integration.Name != models.Gmail && integration.Name != models.Outlook {
		checkUnauthorizedAttempts(ctx, integration, statusCode)
	}

	body := GetHTTPLogJSON(ctx, integration, statusCode)
	if len(body) > 0 {
		//nolint:forbidigo // Needed so Cloudwatch can parse as a metric. It can't when it's a nested JSON object
		// in zap log
		fmt.Println(string(body))
	}
}

// For use in LogHTTPResponseCode and retryablehttp.Client.Logger
func GetHTTPLogJSON(ctx context.Context, integration models.Integration, statusCode int) (res []byte) {
	if !isLoggingEnabled() {
		return
	}

	if lower := strings.ToLower(string(integration.Name)); lower == "" || lower == "null" || lower == "none" {
		log.Error(ctx, fmt.Sprintf("invalid integration name '%s'", integration.Name))
		return
	}

	res, err := json.Marshal(IntegrationResponseCode{
		AxleIntegrationID: integration.ID,
		IntegrationType:   integration.Type,
		IntegrationName:   integration.Name,
		StatusCode:        statusCode},
	)
	if err != nil {
		log.Error(ctx, "jsonMarshal failed", zap.Error(err))
		return
	}

	return res
}

// Report an HTTP request which never reached the server (e.g. "i/o timeout" or "connection reset by peer").
// For clients that use oauth2.NewClient(), it checks for Oauth errors and logs that oauth endpoint's original
// status code instead of 599.
func LogHTTPRequestFailed(ctx context.Context, integration models.Integration, err error) {
	// Oauth client bubbles up oauth endpoint non-2xx in error object instead of in response.
	// Explicitly log those as 401s/403 because we want to know if oauth is continuously failing.
	var oauthError *oauth2.RetrieveError
	if errors.As(err, &oauthError) {
		LogHTTPResponseCode(ctx, integration, 401)
		return
	}

	// Use the non-standard status code 599 Network Connect Timeout Error,
	// which is unlikely to be used by any real integration and lets us distinguish this case
	log.Info(ctx, "integration error 599:", zap.Error(err))
	LogHTTPResponseCode(ctx, integration, 599)
}

// Disable integration logging during local development by default
func isLoggingEnabled() bool {
	return os.Getenv("APP_ENV") != "dev" ||
		strings.ToLower(os.Getenv("DEBUG")) == "true" ||
		os.Getenv("DEBUG") == "1"
}

// checkUnauthorizedAttempts caches 401 responses from integrations in Redis. If the number of consecutive 401s
// is 5 or more, the integration is disabled and a warning is sent to Sentry.
func checkUnauthorizedAttempts(ctx context.Context, integration models.Integration, statusCode int) {
	ctx = log.With(ctx,
		zap.String("integrationName", string(integration.Name)),
		zap.Uint("integrationID", integration.ID),
		zap.Uint("serviceID", integration.ServiceID),
		zap.Int("statusCode", statusCode))

	redisKey := redisUnauthorizedIntegrationAttemptsKey(integration.ID)

	// Attempt to fetch the key using the wrapper
	unauthorizedAttemptsStr, found, redisErr := redis.GetKey[string](ctx, redisKey)

	// Handle any errors from GetKey (other than not found)
	if redisErr != nil && !errors.Is(redisErr, redis.NilEntry) {
		logUnauthorizedCountWarnsNoSentry(
			ctx, "non nil-entry error while querying unauthorized attempts in redis", integration, redisErr,
		)
		return
	}

	if found {
		// Key exists, parse the current count
		unauthorizedAttempts, err := strconv.Atoi(unauthorizedAttemptsStr)
		if err != nil {
			log.WarnNoSentry(ctx, "error parsing unauthorized attempts count", zap.Error(err))
			unauthorizedAttempts = 0 // Default to 0 if parsing fails
		}

		if statusCode != http.StatusUnauthorized && unauthorizedAttempts > 0 {
			// Reset count for non-401 responses
			log.Info(ctx, "resetting unauthorized attempts for integration due to non-401 response")

			if err = redis.SetKey(ctx, redisKey, 0, 0); err != nil {
				logUnauthorizedCountWarnsNoSentry(
					ctx, "error resetting unauthorized integration attempt entry in redis", integration, err,
				)
			}
		} else if statusCode == http.StatusUnauthorized {
			// Increment count for 401 responses
			if _, err = redis.IncrementKey(ctx, redisKey, 0); err != nil {
				logUnauthorizedCountWarnsNoSentry(
					ctx, "error incrementing unauthorized integration attempt entry in redis", integration, err,
				)
				return
			}

			// Check if we've reached the threshold (pre-increment count, so >= 4 means 5th attempt)
			if unauthorizedAttempts >= 4 {
				// GlobalTranz has a higher threshold due to poller's parallel requests
				if integration.Name == models.GlobalTranz && unauthorizedAttempts < 50 {
					return
				}

				logUnauthorizedCountErrors(
					ctx, "disabling integration due to unauthorized status code response", integration, nil,
				)

				if err := integrationDB.Disable(ctx, &integration); err != nil {
					logUnauthorizedCountErrors(
						ctx, "failed disabling integration due to 401 response", integration, err,
					)
				}

				log.Error(ctx, "disabled integration due to multiple unauthorized status code responses")
			}
		}
	} else if statusCode == http.StatusUnauthorized {
		// Key doesn't exist and we have a 401, create new entry with count at 1
		if err := redis.SetKey(ctx, redisKey, 1, 0); err != nil {
			logUnauthorizedCountWarnsNoSentry(
				ctx, "error creating unauthorized integration attempt entry in redis", integration, err,
			)
		}
	}
}

func logUnauthorizedCountErrors(
	ctx context.Context,
	errMessage string,
	integration models.Integration,
	err error,
) {
	log.Error(
		ctx,
		errMessage,
		zap.String("integration name", string(integration.Name)),
		zap.String("integration username", integration.Username),
		zap.Uint("integration service id", integration.ServiceID),
		zap.Error(err),
	)
}

func logUnauthorizedCountWarnsNoSentry(
	ctx context.Context,
	errMessage string,
	integration models.Integration,
	err error,
) {
	log.WarnNoSentry(
		ctx,
		errMessage,
		zap.String("integration name", string(integration.Name)),
		zap.String("integration username", integration.Username),
		zap.Uint("integration service id", integration.ServiceID),
		zap.Error(err),
	)
}
