package warehouse

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func Upsert(ctx context.Context, warehouse *models.Warehouse) error {
	return rds.WithContext(ctx).Clauses(
		clause.Returning{},
		clause.OnConflict{
			Columns: []clause.Column{
				{Name: "warehouse_id"},
				{Name: "source"},
			},
			UpdateAll: true,
		},
	).Create(warehouse).Error
}

func OnboardWarehouses(ctx context.Context, warehouses []models.Warehouse) error {
	return rds.WithContext(ctx).
		Clauses(
			clause.Returning{},
			clause.OnConflict{
				Columns: []clause.Column{
					{Name: "warehouse_id"},
					{Name: "source"},
				},
				UpdateAll: true,
			},
		).
		CreateInBatches(&warehouses, 1000).Error
}

// CreateWarehouseAddress creates an address association from a load
func CreateWarehouseAddress(
	ctx context.Context,
	warehouse models.Warehouse,
	load models.Load,
	requestType models.RequestType,
) error {

	var address models.WarehouseAddress

	switch requestType {
	case models.RequestTypePickup:
		address = models.WarehouseAddress{
			Address: models.ToAddressModel(load.Pickup.CompanyCoreInfo),
		}

	case models.RequestTypeDropoff:
		address = models.WarehouseAddress{
			Address: models.ToAddressModel(load.Consignee.CompanyCoreInfo),
		}

	default:
		return fmt.Errorf("invalid request type: %s", requestType)
	}

	// Debug: Print warehouse and address details
	db := rds.WithContext(ctx)

	// Use FirstOrCreate to handle existing addresses
	var existingAddress models.WarehouseAddress
	err := db.Where(models.WarehouseAddress{
		Address: address.Address,
	}).FirstOrCreate(&existingAddress, address).Error

	if err != nil {
		return fmt.Errorf("failed to find or create warehouse address: %w", err)
	}

	// Check if this specific association already exists
	var existingAssociations []models.WarehouseAddress
	err = db.Model(&warehouse).Association("Addresses").Find(&existingAssociations)
	if err != nil {
		log.Debug(ctx, "Failed to find existing associations", zap.Error(err))
	} else {
		for _, addr := range existingAssociations {
			if addr.ID == existingAddress.ID {
				log.Debug(ctx, "Association already exists - skipping")
				return nil
			}
		}
	}

	// Create the association
	return db.Model(&warehouse).Association("Addresses").Append(&existingAddress)
}
