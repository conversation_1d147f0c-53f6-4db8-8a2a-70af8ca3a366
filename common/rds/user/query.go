package user

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func GetByID(ctx context.Context, id uint) (user models.User, err error) {
	return user, rds.WithContextReader(ctx).Where("id = ?", id).First(&user).Error
}

func GetByOnPremID(ctx context.Context, id uint, email string) (user models.User, err error) {
	return user, rds.WithContextReader(ctx).
		Where("on_prem_id = ? AND lower(email_address) = ?", id, strings.ToLower(email)).
		First(&user).Error
}

func GetByEmail(ctx context.Context, emailAddr string) (user models.User, err error) {
	err = rds.WithContextReader(ctx).
		Preload("Service").
		Where(
			"lower(email_address) = @emailAddr OR lower(secondary_email_address) = @emailAddr",
			sql.Named("emailAddr", strings.ToLower(emailAddr)),
		).
		First(&user).Error

	return user, err
}

func GetByEmailForOnboard(ctx context.Context, emailAddr string) (user models.User, err error) {
	err = rds.WithContextReader(ctx).
		Preload("Service").
		Where(
			"lower(email_address) = @emailAddr OR lower(secondary_email_address) = @emailAddr",
			sql.Named("emailAddr", strings.ToLower(emailAddr)),
		).
		First(&user).Error

	// If we can't find an exact email match, check if email domain exists for any service
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return tryMatchingInvalidEmailDomain(ctx, emailAddr)
	}

	// If we found a matching user, check if email address used for sign-in/sign-up is secondary.
	if user.SecondaryEmailAddress == emailAddr {

		// In that case, swap them so we store the most recent email address as primary.
		user.EmailAddress, user.SecondaryEmailAddress = user.SecondaryEmailAddress, user.EmailAddress
		err = Update(ctx, user)
		if err != nil {
			log.Error(ctx, "error updating user email address for user onboard", zap.Error(err))
		}
	}

	return user, err
}

func GetByOutlookIDs(ctx context.Context, userExternalID, clientState, subID string) (user models.User, err error) {
	return user, rds.WithContextReader(ctx).
		Where(
			"mail_client_id = ? AND outlook_client_state = ? AND outlook_subscription_id = ?",
			userExternalID, clientState, subID,
		).
		First(&user).Error
}

func GetByExternalID(ctx context.Context, userExternalID string) (user models.User, err error) {
	return user, rds.WithContextReader(ctx).Where("mail_client_id = ?", userExternalID).First(&user).Error
}

func GetAll(ctx context.Context) (users []models.User, err error) {
	return users, rds.WithContextReader(ctx).Find(&users).Error
}

func GetAllByServiceID(ctx context.Context, serviceID uint) (users []models.User, err error) {
	return users, rds.WithContextReader(ctx).
		Where("service_id = ?", serviceID).
		Where("deleted_at IS NULL").
		Find(&users).Error
}

func GetByServiceIDAssociatedWithThread(
	ctx context.Context,
	threadID string,
	serviceID uint,
) ([]models.User, error) {
	var users []models.User
	err := rds.WithContextReader(ctx).
		Model(&models.User{}).
		Where(
			"id IN (SELECT DISTINCT user_id FROM emails WHERE thread_id = ? AND service_id = ?)",
			threadID,
			serviceID,
		).
		Find(&users).Error

	return users, err
}

// tryMatchingInvalidEmailDomain handles cases where services rebrand and have multiple domains.
// The first time a user re-logs in with the new domain, we don't want to create a duplicate
// user/service but rather update the existing ones.
//
// Example:
// <EMAIL> exists and is part of a service that supports @axle.com and @drumkit.ai.
// If <EMAIL> signs up we don't want to create a new user/service for @drumkit.ai, but rather
// share existing ones while storing email addresses on primary/secondary email fields.
func tryMatchingInvalidEmailDomain(ctx context.Context, emailAddr string) (models.User, error) {
	emailParts := strings.Split(emailAddr, "@")
	if len(emailParts) != 2 {
		return models.User{}, fmt.Errorf("invalid email format: %s", emailAddr)
	}

	onboardedEmailDomain := fmt.Sprintf("@%s", emailParts[1])
	service, serviceQueryErr := rds.GetServiceByDomain(ctx, onboardedEmailDomain)
	if serviceQueryErr != nil {
		return models.User{}, fmt.Errorf("user not found, error getting service for fallback: %w", serviceQueryErr)
	}

	var foundUser models.User

	for _, domain := range service.EmailDomains {

		onboardedEmailNameWithNewDomain := fmt.Sprintf("%s%s", emailParts[0], domain)
		userQueryErr := rds.WithContextReader(ctx).
			Where(
				"lower(email_address) = @emailAddr OR lower(secondary_email_address) = @emailAddr",
				sql.Named("emailAddr", onboardedEmailNameWithNewDomain),
			).
			First(&foundUser).Error

		if userQueryErr == nil {
			// If we're able to match the sign-in/sign-up email to an existing user we proceed to store it
			// as the primary email, move the previous primary one to secondary.
			foundUser.SecondaryEmailAddress = foundUser.EmailAddress
			foundUser.EmailAddress = emailAddr

			userUpdateErr := Update(ctx, foundUser)
			if userUpdateErr != nil {
				return foundUser, userUpdateErr
			}

			return foundUser, nil
		}
	}

	return models.User{}, gorm.ErrRecordNotFound
}
