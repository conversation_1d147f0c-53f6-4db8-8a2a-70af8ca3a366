package tmscustomerschedulingintegration

import (
	"context"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// CreateOrUpdateAssociation creates a new association or updates an existing one
func CreateOrUpdateAssociation(ctx context.Context, association *models.TMSCustomerSchedulingIntegration) error {
	return rds.WithContext(ctx).Clauses(
		clause.OnConflict{
			Columns: []clause.Column{
				{Name: "tms_customer_id"},
				{Name: "scheduling_integration_id"},
			},
			DoUpdates: clause.AssignmentColumns([]string{
				"usage_count",
				"last_used_at",
				"is_preferred",
				"notes",
				"updated_at",
			}),
		},
	).Create(association).Error
}

// IncrementUsage increments the usage count and updates last used time for an association
func IncrementUsage(ctx context.Context, tmsCustomerID, schedulingIntegrationID uint) error {
	return rds.WithContext(ctx).Model(&models.TMSCustomerSchedulingIntegration{}).
		Where("tms_customer_id = ? AND scheduling_integration_id = ?", tmsCustomerID, schedulingIntegrationID).
		Updates(map[string]interface{}{
			"usage_count":  gorm.Expr("usage_count + 1"),
			"last_used_at": time.Now(),
			"updated_at":   time.Now(),
		}).Error
}

// SetPreferred sets an integration as preferred for a customer (and unsets others)
func SetPreferred(ctx context.Context, tmsCustomerID, schedulingIntegrationID, userID uint) error {
	return rds.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// First, unset all preferred flags for this customer
		if err := tx.Model(&models.TMSCustomerSchedulingIntegration{}).
			Where("tms_customer_id = ?", tmsCustomerID).
			Update("is_preferred", false).Error; err != nil {
			return err
		}

		// Then set the specified integration as preferred
		association := &models.TMSCustomerSchedulingIntegration{
			TMSCustomerID:           tmsCustomerID,
			SchedulingIntegrationID: schedulingIntegrationID,
			IsPreferred:             true,
			LastUsedAt:              time.Now(),
			UsageCount:              1,
			CreatedByUserID:         userID,
		}

		return CreateOrUpdateAssociation(context.WithValue(ctx, "tx", tx), association)
	})
}

// RecordUsage records that a customer used a specific scheduling integration
// This will create the association if it doesn't exist, or increment usage if it does
func RecordUsage(ctx context.Context, tmsCustomerID, schedulingIntegrationID, userID uint) error {
	// First try to increment existing association
	err := IncrementUsage(ctx, tmsCustomerID, schedulingIntegrationID)
	if err == nil {
		return nil
	}

	// If no existing association, create a new one
	if err == gorm.ErrRecordNotFound {
		association := &models.TMSCustomerSchedulingIntegration{
			TMSCustomerID:           tmsCustomerID,
			SchedulingIntegrationID: schedulingIntegrationID,
			IsPreferred:             false,
			LastUsedAt:              time.Now(),
			UsageCount:              1,
			CreatedByUserID:         userID,
		}
		return CreateOrUpdateAssociation(ctx, association)
	}

	return err
}

// DeleteAssociation removes an association between customer and integration
func DeleteAssociation(ctx context.Context, tmsCustomerID, schedulingIntegrationID uint) error {
	return rds.WithContext(ctx).
		Where("tms_customer_id = ? AND scheduling_integration_id = ?", tmsCustomerID, schedulingIntegrationID).
		Delete(&models.TMSCustomerSchedulingIntegration{}).Error
}
