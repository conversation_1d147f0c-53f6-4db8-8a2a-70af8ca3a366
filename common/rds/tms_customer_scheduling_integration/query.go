package tmscustomerschedulingintegration

import (
	"context"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// GetPreferredIntegrationForCustomer returns the preferred scheduling integration for a customer
func GetPreferredIntegrationForCustomer(ctx context.Context, tmsCustomerID uint) (*models.TMSCustomerSchedulingIntegration, error) {
	var association models.TMSCustomerSchedulingIntegration

	err := rds.WithContextReader(ctx).
		Where("tms_customer_id = ? AND is_preferred = ?", tmsCustomerID, true).
		Preload("SchedulingIntegration").
		First(&association).Error

	if err != nil {
		return nil, err
	}

	return &association, nil
}

// GetMostUsedIntegrationForCustomer returns the most frequently used scheduling integration for a customer
func GetMostUsedIntegrationForCustomer(ctx context.Context, tmsCustomerID uint) (*models.TMSCustomerSchedulingIntegration, error) {
	var association models.TMSCustomerSchedulingIntegration

	err := rds.WithContextReader(ctx).
		Where("tms_customer_id = ?", tmsCustomerID).
		Order("usage_count DESC, last_used_at DESC").
		Preload("SchedulingIntegration").
		First(&association).Error

	if err != nil {
		return nil, err
	}

	return &association, nil
}

// GetAllAssociationsForCustomer returns all scheduling integrations associated with a customer
func GetAllAssociationsForCustomer(ctx context.Context, tmsCustomerID uint) ([]models.TMSCustomerSchedulingIntegration, error) {
	var associations []models.TMSCustomerSchedulingIntegration

	err := rds.WithContextReader(ctx).
		Where("tms_customer_id = ?", tmsCustomerID).
		Order("is_preferred DESC, usage_count DESC, last_used_at DESC").
		Preload("SchedulingIntegration").
		Find(&associations).Error

	return associations, err
}

// GetAssociationByCustomerAndIntegration returns a specific association between customer and integration
func GetAssociationByCustomerAndIntegration(ctx context.Context, tmsCustomerID, schedulingIntegrationID uint) (*models.TMSCustomerSchedulingIntegration, error) {
	var association models.TMSCustomerSchedulingIntegration

	err := rds.WithContextReader(ctx).
		Where("tms_customer_id = ? AND scheduling_integration_id = ?", tmsCustomerID, schedulingIntegrationID).
		Preload("SchedulingIntegration").
		First(&association).Error

	if err != nil {
		return nil, err
	}

	return &association, nil
}

// GetRecommendedIntegrationForCustomer returns the best integration choice for a customer
// Priority: 1) Preferred integration, 2) Most used integration, 3) nil if none found
func GetRecommendedIntegrationForCustomer(ctx context.Context, tmsCustomerID uint) (*models.Integration, error) {
	// First try to get preferred integration
	preferred, err := GetPreferredIntegrationForCustomer(ctx, tmsCustomerID)
	if err == nil && preferred != nil {
		return &preferred.SchedulingIntegration, nil
	}

	// If no preferred, get most used
	mostUsed, err := GetMostUsedIntegrationForCustomer(ctx, tmsCustomerID)
	if err == nil && mostUsed != nil {
		return &mostUsed.SchedulingIntegration, nil
	}

	// Return nil if no associations found (not an error - customer just has no preferences)
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}

	return nil, err
}
