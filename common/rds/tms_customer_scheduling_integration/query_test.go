package tmscustomerschedulingintegration

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetRecommendedIntegrationForCustomer(t *testing.T) {
	t.Run("should return nil when no associations exist", func(t *testing.T) {
		ctx := context.Background()

		// Test with a non-existent customer ID
		integration, err := GetRecommendedIntegrationForCustomer(ctx, 99999)

		assert.NoError(t, err)
		assert.Nil(t, integration)
	})
}

func TestGetPreferredIntegrationForCustomer(t *testing.T) {
	t.Run("should return error when customer has no preferred integration", func(t *testing.T) {
		ctx := context.Background()

		// Test with a non-existent customer ID
		association, err := GetPreferredIntegrationForCustomer(ctx, 99999)

		assert.Error(t, err)
		assert.Nil(t, association)
	})
}

func TestGetMostUsedIntegrationForCustomer(t *testing.T) {
	t.Run("should return error when customer has no integrations", func(t *testing.T) {
		ctx := context.Background()

		// Test with a non-existent customer ID
		association, err := GetMostUsedIntegrationForCustomer(ctx, 99999)

		assert.Error(t, err)
		assert.Nil(t, association)
	})
}

func TestGetAllAssociationsForCustomer(t *testing.T) {
	t.Run("should return empty slice when customer has no associations", func(t *testing.T) {
		ctx := context.Background()

		// Test with a non-existent customer ID
		associations, err := GetAllAssociationsForCustomer(ctx, 99999)

		assert.NoError(t, err)
		assert.Empty(t, associations)
	})
}

func TestGetAssociationByCustomerAndIntegration(t *testing.T) {
	t.Run("should return error when association does not exist", func(t *testing.T) {
		ctx := context.Background()

		// Test with non-existent IDs
		association, err := GetAssociationByCustomerAndIntegration(ctx, 99999, 99999)

		assert.Error(t, err)
		assert.Nil(t, association)
	})
}
