package tmscustomerschedulingintegration

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func TestCreateOrUpdateAssociation(t *testing.T) {
	t.Run("should handle invalid association gracefully", func(t *testing.T) {
		ctx := context.Background()
		
		// Test with invalid association (missing required fields)
		association := &models.TMSCustomerSchedulingIntegration{
			// Missing required TMSCustomerID and SchedulingIntegrationID
			IsPreferred: true,
			LastUsedAt:  time.Now(),
			UsageCount:  1,
		}
		
		err := CreateOrUpdateAssociation(ctx, association)
		
		// Should return an error due to foreign key constraints
		assert.Error(t, err)
	})
}

func TestIncrementUsage(t *testing.T) {
	t.Run("should handle non-existent association gracefully", func(t *testing.T) {
		ctx := context.Background()
		
		// Test with non-existent customer and integration IDs
		err := IncrementUsage(ctx, 99999, 99999)
		
		// Should not return an error even if no rows are affected
		assert.NoError(t, err)
	})
}

func TestSetPreferred(t *testing.T) {
	t.Run("should handle invalid customer ID gracefully", func(t *testing.T) {
		ctx := context.Background()
		
		// Test with non-existent customer and integration IDs
		err := SetPreferred(ctx, 99999, 99999, 1)
		
		// Should return an error due to foreign key constraints
		assert.Error(t, err)
	})
}

func TestRecordUsage(t *testing.T) {
	t.Run("should handle invalid customer ID gracefully", func(t *testing.T) {
		ctx := context.Background()
		
		// Test with non-existent customer and integration IDs
		err := RecordUsage(ctx, 99999, 99999, 1)
		
		// Should return an error due to foreign key constraints
		assert.Error(t, err)
	})
}

func TestDeleteAssociation(t *testing.T) {
	t.Run("should handle non-existent association gracefully", func(t *testing.T) {
		ctx := context.Background()
		
		// Test with non-existent customer and integration IDs
		err := DeleteAssociation(ctx, 99999, 99999)
		
		// Should not return an error even if no rows are affected
		assert.NoError(t, err)
	})
}
