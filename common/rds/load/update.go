package load

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
	"github.com/drumkitai/drumkit/common/redis"
)

func redisLoadAddressWarehouseKey(address string) string {
	return fmt.Sprintf("load-address-%s", address)
}

func UpsertLoad(ctx context.Context, newLoad *models.Load, service *models.Service) error {
	isNewLoad := true

	// Update dates before upsert
	if err := newLoad.UpdateDates(); err != nil {
		log.WarnNoSentry(ctx, "failed to update load dates during upsert - continuing with existing dates",
			zap.Error(err),
			zap.String("freightTrackingID", newLoad.FreightTrackingID))
	}

	if len(newLoad.Pickup.Zipcode) >= 3 {
		newLoad.Pickup.ZipPrefix = newLoad.Pickup.Zipcode[:3]
	}

	if len(newLoad.Consignee.Zipcode) >= 3 {
		newLoad.Consignee.ZipPrefix = newLoad.Consignee.Zipcode[:3]
	}

	// Optimization: Do this only if the service has appointment scheduling enabled
	// If service is nil, default to matching
	if service == nil || (service.ID != 0 && service.IsAppointmentSchedulingEnabled) {
		// Try matching warehouses if load doesn't exist in DB or if it has no associated warehouses
		dbLoad, err := GetLoadByFreightIDAndTMSID(ctx, newLoad.TMSID, newLoad.FreightTrackingID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				log.Debug(
					ctx,
					"no existing load in DB, matching warehouses",
					zap.String("freightTrackingID", newLoad.FreightTrackingID),
					zap.Uint("tmsID", newLoad.TMSID),
				)

				matchLoadPickupToWarehouse(ctx, newLoad)
				matchLoadDropoffToWarehouse(ctx, newLoad)
			} else {
				log.Error(
					ctx,
					"error getting load from DB",
					zap.Error(err),
					zap.String("freightTrackingID", newLoad.FreightTrackingID),
					zap.Uint("tmsID", newLoad.TMSID))
			}
		} else {
			// if the load exists in rds, it's not a new load
			isNewLoad = false

			if dbLoad.PickupWarehouseID != 0 && dbLoad.Pickup.CompanyCoreInfo == newLoad.Pickup.CompanyCoreInfo {
				newLoad.PickupWarehouseID = dbLoad.PickupWarehouseID
			} else {
				matchLoadPickupToWarehouse(ctx, newLoad)
			}

			if dbLoad.DropoffWarehouseID != 0 && dbLoad.Consignee.CompanyCoreInfo == newLoad.Consignee.CompanyCoreInfo {
				newLoad.DropoffWarehouseID = dbLoad.DropoffWarehouseID
			} else {
				matchLoadDropoffToWarehouse(ctx, newLoad)
			}
		}
	}

	// If isNewLoad is true, the warehouse checks may have failed, so we need to re-check if the load exists in rds
	if isNewLoad {
		_, existingLoadErr := GetLoadByFreightIDAndTMSID(ctx, newLoad.TMSID, newLoad.FreightTrackingID)
		if existingLoadErr == nil {
			isNewLoad = false
		}
	}

	// FIXME: Updates a warehouse but does not reset to 0/no warehouse
	// https://github.com/go-gorm/gen/issues/618 - Workaround for preloading on create
	err := rds.WithContext(ctx).Clauses(
		clause.Returning{},
		clause.OnConflict{
			Columns:   []clause.Column{{Name: "freight_tracking_id"}, {Name: "service_id"}},
			UpdateAll: true}).
		Create(&newLoad).
		Preload("PickupWarehouse").
		Preload("DropoffWarehouse").
		First(newLoad).Error

	if err == nil && isNewLoad {
		// Only associate load with quote request if it's a new load and was upserted successfully in the DB
		newErr := quoteRequestDB.AssociateLoadWithQuoteRequest(ctx, nil, *newLoad)
		if newErr != nil {
			log.WarnNoSentry(ctx, "error associating load with quote request", zap.Error(newErr))
		}
	}

	return err
}

func matchPickupAddressToWarehouse(
	ctx context.Context,
	newLoad models.Load,
) (warehouseID uint, err error) {
	var pickupWarehouse *models.Warehouse

	// Querying TMS instead of accessing through newLoad.TMS to prevent errors when it's not .Preload()'d
	loadTMS, err := integrationDB.Get(ctx, newLoad.TMSID)
	if err != nil {
		log.Warn(ctx, "error getting TMS to match load pickup address to warehouse",
			zap.Error(err), zap.String("TMS ID", fmt.Sprint(newLoad.TMSID)))
		return 0, err
	}

	pickupAddressParts := []string{
		newLoad.Pickup.AddressLine1,
		newLoad.Pickup.AddressLine2,
		newLoad.Pickup.City,
		newLoad.Pickup.State,
		newLoad.Pickup.Zipcode,
	}
	pickupAddress := strings.Join(pickupAddressParts, " ")

	warehouseLookupKey := redisLoadAddressWarehouseKey(pickupAddress)
	cachedWarehouseID, redisErr := checkLoadAddressForWarehouseRedis(ctx, warehouseLookupKey)
	if redisErr == nil {
		return cachedWarehouseID, nil
	}

	pickupWarehouse, err = warehouseDB.GetWarehouseByAddress(
		ctx,
		loadTMS.ServiceID,
		pickupAddress,
		extractStreetNumber(newLoad.Pickup.AddressLine1),
	)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnNoSentry(ctx, "no entries found while matching load pickup address to warehouse",
				zap.Error(err), zap.String("pickupAddress", pickupAddress))

			// Cache even when result is empty to prevent future redundant lookups
			err = redis.SetKey(ctx, warehouseLookupKey, 0, 12*time.Hour)
			if err != nil {
				log.Warn(ctx, "failed to set pickup address warehouse association in Redis", zap.Error(err))
			}
		} else {
			log.Warn(ctx, "error matching load pickup address to warehouse",
				zap.Error(err), zap.String("pickupAddress", pickupAddress))
		}

		return 0, err
	}

	err = redis.SetKey(ctx, warehouseLookupKey, pickupWarehouse.ID, 12*time.Hour)
	if err != nil {
		log.Warn(ctx, "failed to set load pickup warehouse association in Redis", zap.Error(err),
			zap.Uint("warehouseID", pickupWarehouse.ID))
	}

	return pickupWarehouse.ID, nil
}

// TODO: Simplify to just 1 function using generic models.Stop
func matchConsigneeAddressToWarehouse(
	ctx context.Context,
	newLoad models.Load,
) (warehouseID uint, err error) {
	var consigneeWarehouse *models.Warehouse

	// Querying TMS instead of accessing through newLoad.TMS to prevent errors when it's not .Preload()'d
	loadTMS, err := integrationDB.Get(ctx, newLoad.TMSID)
	if err != nil {
		log.Warn(
			ctx,
			"error getting TMS to match load consignee address to warehouse",
			zap.Error(err),
			zap.String("TMS ID", fmt.Sprint(newLoad.TMSID)),
		)
		return 0, err
	}

	consigneeAddressParts := []string{
		newLoad.Consignee.AddressLine1,
		newLoad.Consignee.AddressLine2,
		newLoad.Consignee.City,
		newLoad.Consignee.State,
		newLoad.Consignee.Zipcode,
	}
	consigneeAddress := strings.Join(consigneeAddressParts, " ")

	warehouseLookupKey := redisLoadAddressWarehouseKey(consigneeAddress)
	cachedWarehouseID, redisErr := checkLoadAddressForWarehouseRedis(ctx, warehouseLookupKey)
	if redisErr == nil {
		return cachedWarehouseID, nil
	}

	consigneeWarehouse, err = warehouseDB.GetWarehouseByAddress(
		ctx,
		loadTMS.ServiceID,
		consigneeAddress,
		extractStreetNumber(newLoad.Consignee.AddressLine1),
	)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnNoSentry(ctx, "no entries found while matching load consignee address to warehouse",
				zap.Error(err), zap.String("consigneeAddress", consigneeAddress))

			// Cache even when result is empty to prevent future redundant lookups
			err = redis.SetKey(ctx, warehouseLookupKey, 0, 12*time.Hour)
			if err != nil {
				log.Warn(ctx, "failed to set consignee address warehouse association in Redis", zap.Error(err))
			}
		} else {
			log.Warn(ctx, "error matching load consignee address to warehouse",
				zap.Error(err), zap.String("consigneeAddress", consigneeAddress))
		}

		return 0, err
	}

	err = redis.SetKey(ctx, warehouseLookupKey, consigneeWarehouse.ID, 12*time.Hour)
	if err != nil {
		log.Warn(ctx, "failed to set load consignee warehouse association in Redis", zap.Error(err),
			zap.Uint("warehouseID", consigneeWarehouse.ID))
	}

	return consigneeWarehouse.ID, nil
}

func matchLoadPickupToWarehouse(ctx context.Context, newLoad *models.Load) {
	matchedPickupWarehouseID, err := matchPickupAddressToWarehouse(ctx, *newLoad)
	if err == nil {
		newLoad.PickupWarehouseID = matchedPickupWarehouseID
	} else {
		log.WarnNoSentry(ctx, "load upsert couldn't match pickup warehouse:", zap.Error(err))
	}
}

func matchLoadDropoffToWarehouse(ctx context.Context, newLoad *models.Load) {
	matchedDropoffWarehouseID, err := matchConsigneeAddressToWarehouse(ctx, *newLoad)
	if err == nil {
		newLoad.DropoffWarehouseID = matchedDropoffWarehouseID
	} else {
		log.WarnNoSentry(ctx, "load upsert couldn't match dropoff warehouse:", zap.Error(err))
	}
}

func checkLoadAddressForWarehouseRedis(ctx context.Context, addressLookupKey string) (uint, error) {
	cachedWarehouseIDStr, _, err := redis.GetKey[string](ctx, addressLookupKey)
	if err == nil && cachedWarehouseIDStr != "" {
		log.Info(ctx, "found warehouse by address lookup in Redis", zap.String("address", addressLookupKey))

		cachedWarehouseID, err := strconv.ParseUint(cachedWarehouseIDStr, 10, 64)
		if err != nil {
			log.Error(ctx, "error converting cached warehouse id into uint",
				zap.Error(err), zap.String("cachedWarehouseIDStr", cachedWarehouseIDStr))
			return 0, fmt.Errorf("error converting cached warehouse id into uint: %w", err)
		}

		return uint(cachedWarehouseID), nil
	}

	return 0, err
}

func extractStreetNumber(address string) string {
	re := regexp.MustCompile(`^\d+\b`)
	match := re.FindString(address)
	return match
}
